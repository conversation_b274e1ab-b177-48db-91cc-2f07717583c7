#!/usr/bin/env python3
"""
Test script to verify the verification-status endpoint is working correctly
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
USERNAME = "admin"
PASSWORD = "AdminPass123!@#"

def test_verification_status():
    """Test the verification status endpoint"""
    
    # Step 1: Login to get token
    print("🔐 Logging in...")
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/api/auth/token", data=login_data)
    if response.status_code != 200:
        print(f"❌ Login failed: {response.status_code} - {response.text}")
        return False
    
    token_data = response.json()
    token = token_data.get("access_token")
    if not token:
        print("❌ No access token received")
        return False
    
    print("✅ Login successful")
    
    # Step 2: Test verification status endpoint
    print("📊 Testing verification status endpoint...")
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(f"{BASE_URL}/api/assets/verification-status", headers=headers)
    
    if response.status_code == 200:
        print("✅ Verification status endpoint working!")
        data = response.json()
        
        # Print summary
        summary = data.get("summary", {})
        print(f"📈 Summary:")
        print(f"   Total Assets: {summary.get('total_assets', 0)}")
        print(f"   Active Verifications: {summary.get('active_verifications', 0)}")
        print(f"   Overdue Verifications: {summary.get('overdue_verifications', 0)}")
        print(f"   Upcoming Verifications: {summary.get('upcoming_verifications', 0)}")
        print(f"   Exceptions: {summary.get('exceptions', 0)}")
        
        return True
    else:
        print(f"❌ Verification status endpoint failed: {response.status_code}")
        print(f"Response: {response.text}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Asset Manager Verification Status Functionality")
    print("=" * 60)
    
    try:
        success = test_verification_status()
        if success:
            print("\n🎉 All tests passed! Verification status functionality is working correctly.")
        else:
            print("\n❌ Tests failed. Please check the logs above.")
    except Exception as e:
        print(f"\n💥 Test failed with exception: {e}")
