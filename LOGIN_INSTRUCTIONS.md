# Asset Registry - Login Instructions

## 🔐 Login Credentials

**Username:** `admin`
**Password:** `AdminPass123!@#`

## 🚀 How to Access the Application

1. **Open your browser** and go to: http://localhost:3000

2. **If you see a login page:**
   - Enter the credentials above
   - Click "Login"

3. **If you see "Asset not found" or authentication errors:**
   - Click the "Refresh" button on the error message
   - Or refresh the browser page (F5 or Ctrl+R)
   - You may be redirected to the login page

4. **After successful login, you should see:**
   - Dashboard with asset statistics
   - Navigation sidebar with Assets, Verification, Reports
   - User info in the top-right corner

## 🔧 Troubleshooting

### If you can't access the application:

1. **Check if services are running:**
   - Backend: http://localhost:8000/health (should return `{"status": "healthy"}`)
   - Frontend: http://localhost:3000 (should show the login page or dashboard)

2. **Clear browser storage:**
   - Open browser developer tools (F12)
   - Go to Application/Storage tab
   - Clear localStorage and sessionStorage
   - Refresh the page

3. **Check browser console:**
   - Open developer tools (F12)
   - Look for any error messages in the Console tab
   - Network tab should show successful API calls

## 📊 Available Features

After logging in as **Admin**, you can:

- **Dashboard:** View asset statistics and recent activities
- **Assets:** Create, edit, delete, and search assets
- **Verification:** Manage asset verification campaigns
- **Reports:** Generate and download asset reports
- **User Management:** Create and manage user accounts
- **System Configuration:** Configure system modules and settings
- **Audit & Compliance:** Access audit logs, compliance reports, and verification issues

## 👥 User Roles

### Admin Role
- Full system access
- Can create, edit, and delete all data
- Access to user management and system configuration
- All audit and compliance features

### Auditor Role (Read-Only)
- **Username:** `auditor` (create this user through User Management)
- **Password:** `AuditorPass123!` (meets policy requirements)
- View-only access to all assets and data
- Full access to audit logs and compliance reports
- Cannot create, edit, or delete any records
- Specialized audit and compliance dashboard

## 🎯 Test User Creation (Admin Only)

1. Go to **User Management** page (Admin section in sidebar)
2. Click "ADD USER" button
3. Fill in required fields:
   - Username: `auditor`
   - Email: `<EMAIL>`
   - Full Name: `System Auditor`
   - Role: `Auditor`
   - Department: `Compliance`
   - Phone: `1234567890`
   - Password: `AuditorPass123!` (meets policy requirements)
4. Click "CREATE"

The auditor user should be created successfully and appear in the users table.

## 🎯 Test Asset Creation

1. Go to Assets page
2. Click "Add Asset" button
3. Fill in required fields:
   - Asset Tag: `TEST-001`
   - Name: `Test Laptop`
   - Location: `Building A - Floor 1`
   - Assigned To: `John Doe`
4. Click "Add Asset"

The asset should be created successfully and appear in the assets table.

## 🔍 Test Auditor Role

1. **Logout** from admin account
2. **Login** with auditor credentials:
   - Username: `auditor`
   - Password: `AuditorPass123!`
3. **Verify read-only access:**
   - Can view assets but no "Add Asset" button
   - Can access "Audit & Compliance" section
   - Cannot access "Administration" section
   - All data is view-only
