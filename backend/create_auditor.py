#!/usr/bin/env python3
"""
Script to create an auditor user for testing
"""
import asyncio
import sys
import os
from datetime import datetime
from bson import ObjectId

# Add the app directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import get_database_client
from app.routers.auth import get_password_hash

async def create_auditor_user():
    """Create an auditor user in the database"""
    
    # Connect to database
    client = get_database_client()
    db = client.asset_registry
    
    # Check if auditor already exists
    existing_auditor = await db.users.find_one({"username": "auditor"})
    if existing_auditor:
        print("Auditor user already exists!")
        return
    
    # Create auditor user
    auditor_data = {
        "_id": ObjectId(),
        "username": "auditor",
        "email": "<EMAIL>",
        "full_name": "System Auditor",
        "role": "auditor",
        "department": "Compliance",
        "phone": "1234567890",
        "hashed_password": get_password_hash("AuditorPass123!"),
        "status": "active",
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
        "password_changed_at": datetime.utcnow(),
        "failed_login_attempts": 0,
        "password_history": [get_password_hash("AuditorPass123!")]
    }
    
    # Insert user
    result = await db.users.insert_one(auditor_data)
    print(f"Auditor user created with ID: {result.inserted_id}")
    
    # Close connection
    client.close()

if __name__ == "__main__":
    asyncio.run(create_auditor_user())
