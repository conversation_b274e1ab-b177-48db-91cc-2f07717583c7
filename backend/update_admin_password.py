#!/usr/bin/env python3
"""
<PERSON>ript to update admin password to meet new policy requirements
"""
import asyncio
import sys
import os
from datetime import datetime
from motor.motor_asyncio import AsyncIOMotorClient
from passlib.context import Crypt<PERSON>ontext

# Add the app directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

async def update_admin_password():
    """Update admin password to meet new policy requirements"""
    
    print("Connecting to MongoDB...")
    client = AsyncIOMotorClient(settings.MONGODB_URL)
    db = client.asset_registry
    
    try:
        # Test connection
        await client.admin.command('ping')
        print("✓ Connected to MongoDB successfully")
        
        # Find admin user
        admin_user = await db.users.find_one({"username": "admin"})
        if not admin_user:
            print("❌ Admin user not found!")
            return
        
        print("✓ Found admin user")
        
        # Update password to meet new policy (minimum 12 characters)
        new_password = "AdminPass123!@#"
        hashed_password = pwd_context.hash(new_password)
        
        # Update admin user with new password
        update_result = await db.users.update_one(
            {"username": "admin"},
            {
                "$set": {
                    "hashed_password": hashed_password,
                    "password_changed_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow(),
                    "failed_login_attempts": 0,
                    "locked_until": None
                },
                "$push": {
                    "password_history": hashed_password
                }
            }
        )
        
        if update_result.modified_count > 0:
            print("✓ Admin password updated successfully!")
            print(f"  New password: {new_password}")
            print("  ⚠️  Password now meets ISO 27001 policy requirements:")
            print("     - Minimum 12 characters ✓")
            print("     - Contains uppercase letters ✓")
            print("     - Contains lowercase letters ✓")
            print("     - Contains numbers ✓")
            print("     - Contains special characters ✓")
        else:
            print("❌ Failed to update admin password")
        
    except Exception as e:
        print(f"❌ Error updating password: {e}")
    finally:
        client.close()
        print("✓ Database connection closed")

if __name__ == "__main__":
    asyncio.run(update_admin_password())
