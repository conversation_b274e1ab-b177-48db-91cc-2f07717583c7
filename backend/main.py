from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from app.core.config import settings
from app.core.database import connect_to_mongo, close_mongo_connection
from app.routers import auth, assets, verification, depreciation, wip, audit, reports, dashboard, users, system_config

app = FastAPI(
    title="Asset Registry API",
    description="Comprehensive asset management system with barcode scanning, verification, and audit capabilities",
    version="1.0.0"
)

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    print(f"Validation error: {exc.errors()}")
    return JSONResponse(
        status_code=422,
        content={"detail": exc.errors()}
    )

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000", "*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Database connection events
app.add_event_handler("startup", connect_to_mongo)
app.add_event_handler("shutdown", close_mongo_connection)

# Include routers
app.include_router(auth.router, prefix="/api/auth", tags=["authentication"])
app.include_router(assets.router, prefix="/api/assets", tags=["assets"])
app.include_router(verification.router, prefix="/api/verification", tags=["verification"])
app.include_router(depreciation.router, prefix="/api/depreciation", tags=["depreciation"])
app.include_router(wip.router, prefix="/api/wip", tags=["wip"])
app.include_router(audit.router, prefix="/api/audit", tags=["audit"])
app.include_router(reports.router, prefix="/api/reports", tags=["reports"])
app.include_router(dashboard.router, prefix="/api/dashboard", tags=["dashboard"])
app.include_router(users.router, prefix="/api/users", tags=["user-management"])
app.include_router(system_config.router, prefix="/api/system", tags=["system-configuration"])

@app.get("/")
async def root():
    return {"message": "Asset Registry API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}
