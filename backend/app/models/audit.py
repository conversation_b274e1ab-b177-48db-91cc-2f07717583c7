from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum

class AuditAction(str, Enum):
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    TRANSFER = "transfer"
    VERIFY = "verify"
    LOGIN = "login"
    LOGOUT = "logout"
    PASSWORD_CHANGE = "password_change"
    ROLE_CHANGE = "role_change"
    DEPRECIATION_CALC = "depreciation_calculation"
    PHOTO_UPLOAD = "photo_upload"
    BARCODE_SCAN = "barcode_scan"

class AuditTrailBase(BaseModel):
    action: AuditAction = Field(..., description="Action performed")
    resource_type: str = Field(..., description="Type of resource (asset, user, etc.)")
    resource_id: Optional[str] = Field(None, description="ID of the resource")
    details: Dict[str, Any] = Field(default={}, description="Additional details about the action")
    ip_address: Optional[str] = Field(None, description="IP address of the user")
    user_agent: Optional[str] = Field(None, description="User agent string")

class AuditTrailCreate(AuditTrailBase):
    pass

class AuditTrail(AuditTrailBase):
    id: Optional[str] = Field(None, alias="_id")
    user_id: str = Field(..., description="ID of user who performed the action")
    username: str = Field(..., description="Username of the user")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        populate_by_name = True
        json_schema_extra = {
            "example": {
                "action": "update",
                "resource_type": "asset",
                "resource_id": "AST-001",
                "user_id": "user123",
                "username": "john.doe",
                "details": {
                    "field_changed": "location",
                    "old_value": "Building A",
                    "new_value": "Building B"
                },
                "ip_address": "*************",
                "timestamp": "2023-12-01T10:30:00Z"
            }
        }
