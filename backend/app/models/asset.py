from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from enum import Enum

class DepreciationClass(str, Enum):
    BUILDING = "building"
    FURNITURE = "furniture"
    EQUIPMENT = "equipment"
    VEHICLE = "vehicle"
    IT_EQUIPMENT = "it_equipment"
    OTHER = "other"

class AssetStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    DISPOSED = "disposed"
    MAINTENANCE = "maintenance"
    UNDER_MAINTENANCE = "under_maintenance"
    LOST = "lost"
    WIP = "wip"  # Work in Progress
    UNDER_VERIFICATION = "under_verification"

class CustodianAssignment(BaseModel):
    user_id: str = Field(..., description="User ID of the custodian")
    username: str = Field(..., description="Username of the custodian")
    full_name: str = Field(..., description="Full name of the custodian")
    email: str = Field(..., description="Email of the custodian")
    assigned_date: datetime = Field(default_factory=datetime.utcnow, description="Date assigned")
    assigned_by: str = Field(..., description="User who made the assignment")
    is_primary: bool = Field(default=False, description="Whether this is the primary custodian")
    notification_sent: bool = Field(default=False, description="Whether notification was sent")

class AssetBase(BaseModel):
    asset_id: str = Field(..., description="Unique asset identifier")
    description: str = Field(..., description="Asset description")
    location: str = Field(..., description="Current location of the asset")
    current_location: Optional[str] = Field(None, description="Real-time physical location of the asset")
    custodian: str = Field(..., description="Primary custodian name (for backward compatibility)")
    custodians: List[CustodianAssignment] = Field(default=[], description="List of assigned custodians")
    barcode: Optional[str] = Field(None, description="Barcode identifier")
    purchase_date: datetime = Field(..., description="Date of purchase")
    depreciation_class: DepreciationClass = Field(..., description="Depreciation classification")
    purchase_cost: float = Field(..., ge=0, description="Original purchase cost")
    useful_life_years: int = Field(..., ge=1, description="Useful life in years")
    status: AssetStatus = Field(default=AssetStatus.ACTIVE, description="Current asset status")
    serial_number: Optional[str] = Field(None, description="Serial number")
    manufacturer: Optional[str] = Field(None, description="Manufacturer")
    model: Optional[str] = Field(None, description="Model")
    category: Optional[str] = Field(None, description="Asset category")
    subcategory: Optional[str] = Field(None, description="Asset subcategory")
    notes: Optional[str] = Field(None, description="Additional notes")
    last_verification_date: Optional[datetime] = Field(None, description="Last verification date")
    next_verification_due: Optional[datetime] = Field(None, description="Next verification due date")
    verification_frequency_days: int = Field(default=365, description="Verification frequency in days")
    maintenance_date: Optional[datetime] = Field(None, description="Last maintenance date")
    disposed_date: Optional[datetime] = Field(None, description="Date when asset was disposed")

class AssetCreate(AssetBase):
    pass

class CustodianAssignmentRequest(BaseModel):
    user_id: str = Field(..., description="User ID to assign as custodian")
    is_primary: bool = Field(default=False, description="Whether this should be the primary custodian")

class BulkCustodianAssignmentRequest(BaseModel):
    custodians: List[CustodianAssignmentRequest] = Field(..., description="List of custodians to assign")
    replace_existing: bool = Field(default=False, description="Whether to replace existing custodians")

class AssetUpdate(BaseModel):
    description: Optional[str] = None
    location: Optional[str] = None
    current_location: Optional[str] = None
    custodian: Optional[str] = None
    custodians: Optional[List[CustodianAssignment]] = None
    barcode: Optional[str] = None
    status: Optional[AssetStatus] = None
    serial_number: Optional[str] = None
    manufacturer: Optional[str] = None
    model: Optional[str] = None
    category: Optional[str] = None
    subcategory: Optional[str] = None
    notes: Optional[str] = None
    verification_frequency_days: Optional[int] = None
    maintenance_date: Optional[datetime] = None
    disposed_date: Optional[datetime] = None

class Asset(AssetBase):
    id: Optional[str] = Field(None, alias="_id")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: str = Field(..., description="User who created the asset")
    updated_by: str = Field(..., description="User who last updated the asset")
    current_value: Optional[float] = Field(None, description="Current depreciated value")
    accumulated_depreciation: Optional[float] = Field(None, description="Total depreciation to date")
    photos: List[str] = Field(default=[], description="List of photo URLs")
    
    class Config:
        populate_by_name = True
        json_schema_extra = {
            "example": {
                "asset_id": "AST-001",
                "description": "Dell Laptop Computer",
                "location": "Office Building A, Floor 2",
                "custodian": "John Doe",
                "barcode": "123456789",
                "purchase_date": "2023-01-15T00:00:00Z",
                "depreciation_class": "it_equipment",
                "purchase_cost": 1500.00,
                "useful_life_years": 3,
                "status": "active",
                "serial_number": "DL123456",
                "manufacturer": "Dell",
                "model": "Latitude 5520",
                "category": "IT Equipment",
                "subcategory": "Laptop",
                "maintenance_date": "2023-06-15T00:00:00Z",
                "disposed_date": None
            }
        }
