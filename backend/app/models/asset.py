from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from enum import Enum

class DepreciationClass(str, Enum):
    BUILDING = "building"
    FURNITURE = "furniture"
    EQUIPMENT = "equipment"
    VEHICLE = "vehicle"
    IT_EQUIPMENT = "it_equipment"
    OTHER = "other"

class AssetStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    DISPOSED = "disposed"
    MAINTENANCE = "maintenance"
    UNDER_MAINTENANCE = "under_maintenance"
    LOST = "lost"

class AssetBase(BaseModel):
    asset_id: str = Field(..., description="Unique asset identifier")
    description: str = Field(..., description="Asset description")
    location: str = Field(..., description="Current location of the asset")
    current_location: Optional[str] = Field(None, description="Real-time physical location of the asset")
    custodian: str = Field(..., description="Person responsible for the asset")
    barcode: Optional[str] = Field(None, description="Barcode identifier")
    purchase_date: datetime = Field(..., description="Date of purchase")
    depreciation_class: DepreciationClass = Field(..., description="Depreciation classification")
    purchase_cost: float = Field(..., ge=0, description="Original purchase cost")
    useful_life_years: int = Field(..., ge=1, description="Useful life in years")
    status: AssetStatus = Field(default=AssetStatus.ACTIVE, description="Current asset status")
    serial_number: Optional[str] = Field(None, description="Serial number")
    manufacturer: Optional[str] = Field(None, description="Manufacturer")
    model: Optional[str] = Field(None, description="Model")
    category: Optional[str] = Field(None, description="Asset category")
    subcategory: Optional[str] = Field(None, description="Asset subcategory")
    notes: Optional[str] = Field(None, description="Additional notes")

class AssetCreate(AssetBase):
    pass

class AssetUpdate(BaseModel):
    description: Optional[str] = None
    location: Optional[str] = None
    current_location: Optional[str] = None
    custodian: Optional[str] = None
    barcode: Optional[str] = None
    status: Optional[AssetStatus] = None
    serial_number: Optional[str] = None
    manufacturer: Optional[str] = None
    model: Optional[str] = None
    category: Optional[str] = None
    subcategory: Optional[str] = None
    notes: Optional[str] = None

class Asset(AssetBase):
    id: Optional[str] = Field(None, alias="_id")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: str = Field(..., description="User who created the asset")
    updated_by: str = Field(..., description="User who last updated the asset")
    current_value: Optional[float] = Field(None, description="Current depreciated value")
    accumulated_depreciation: Optional[float] = Field(None, description="Total depreciation to date")
    photos: List[str] = Field(default=[], description="List of photo URLs")
    
    class Config:
        populate_by_name = True
        json_schema_extra = {
            "example": {
                "asset_id": "AST-001",
                "description": "Dell Laptop Computer",
                "location": "Office Building A, Floor 2",
                "custodian": "John Doe",
                "barcode": "123456789",
                "purchase_date": "2023-01-15T00:00:00Z",
                "depreciation_class": "it_equipment",
                "purchase_cost": 1500.00,
                "useful_life_years": 3,
                "status": "active",
                "serial_number": "DL123456",
                "manufacturer": "Dell",
                "model": "Latitude 5520",
                "category": "IT Equipment",
                "subcategory": "Laptop"
            }
        }
