from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional, Dict, Any
from datetime import datetime
from app.models.system_config import (
    SystemConfiguration, ConfigurationItem, ConfigurationUpdate, 
    ConfigurationHistory, SystemModule, ModuleStatus, ConfigCategory,
    DEFAULT_MODULES, DEFAULT_SETTINGS
)
from app.models.user import User, UserRole
from app.models.audit import AuditAction
from app.core.database import get_database
from app.routers.auth import get_current_user
from app.services.audit_service import log_audit_event
from bson import ObjectId

router = APIRouter()

def require_admin_role(current_user: User = Depends(get_current_user)):
    """Dependency to ensure only admin users can access system configuration"""
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin role required"
        )
    return current_user

def config_helper(config) -> dict:
    """Helper function to convert MongoDB document to dict"""
    if config:
        config["id"] = str(config["_id"])
        config.pop("_id", None)

        # Convert datetime fields to ISO format strings if they exist
        for field in ["created_at", "updated_at"]:
            if field in config and config[field] is not None:
                config[field] = config[field].isoformat() if hasattr(config[field], 'isoformat') else config[field]

        return config
    return None

async def get_or_create_system_config(db):
    """Get existing system configuration or create default one"""
    config = await db.system_config.find_one({})

    if not config:
        # Convert Pydantic models to dictionaries for MongoDB storage
        modules_dict = {}
        for name, module in DEFAULT_MODULES.items():
            modules_dict[name] = module.dict()

        settings_dict = {}
        for key, setting in DEFAULT_SETTINGS.items():
            settings_dict[key] = setting.dict()

        # Create default configuration
        default_config = {
            "_id": ObjectId(),
            "modules": modules_dict,
            "settings": settings_dict,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "updated_by": "system",
            "version": "1.0.0"
        }
        await db.system_config.insert_one(default_config)
        config = default_config

    return config

@router.get("/", response_model=SystemConfiguration)
async def get_system_configuration(
    current_user: User = Depends(require_admin_role),
    db = Depends(get_database)
):
    """Get current system configuration"""

    config = await get_or_create_system_config(db)
    return config_helper(config)

@router.get("/modules", response_model=Dict[str, SystemModule])
async def get_system_modules(
    current_user: User = Depends(require_admin_role),
    db = Depends(get_database)
):
    """Get all system modules configuration"""
    
    config = await get_or_create_system_config(db)
    return config.get("modules", {})

@router.get("/modules/{module_name}", response_model=SystemModule)
async def get_module_configuration(
    module_name: str,
    current_user: User = Depends(require_admin_role),
    db = Depends(get_database)
):
    """Get specific module configuration"""
    
    config = await get_or_create_system_config(db)
    modules = config.get("modules", {})
    
    if module_name not in modules:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Module '{module_name}' not found"
        )
    
    return modules[module_name]

@router.put("/modules/{module_name}/status")
async def update_module_status(
    module_name: str,
    status: ModuleStatus,
    reason: Optional[str] = None,
    current_user: User = Depends(require_admin_role),
    db = Depends(get_database)
):
    """Enable, disable, or set maintenance mode for a module"""
    
    config = await get_or_create_system_config(db)
    modules = config.get("modules", {})
    
    if module_name not in modules:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Module '{module_name}' not found"
        )
    
    # Check dependencies before disabling
    if status == ModuleStatus.DISABLED:
        dependent_modules = []
        for mod_name, mod_config in modules.items():
            if module_name in mod_config.get("dependencies", []):
                if mod_config.get("status") == ModuleStatus.ENABLED:
                    dependent_modules.append(mod_name)
        
        if dependent_modules:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot disable module. Dependent modules: {', '.join(dependent_modules)}"
            )
    
    # Update module status
    old_status = modules[module_name]["status"]
    await db.system_config.update_one(
        {},
        {
            "$set": {
                f"modules.{module_name}.status": status,
                "updated_at": datetime.utcnow(),
                "updated_by": current_user.username
            }
        }
    )
    
    # Log configuration change
    await log_config_change(
        db, current_user, f"modules.{module_name}.status",
        old_status, status, reason, ConfigCategory.MODULES
    )
    
    # Log audit event
    await log_audit_event(
        db, str(current_user.id), current_user.username, AuditAction.UPDATE,
        "system_config", f"module_{module_name}",
        {"status": status, "reason": reason},
        None
    )
    
    return {
        "message": f"Module '{module_name}' status updated to {status}",
        "module": module_name,
        "old_status": old_status,
        "new_status": status
    }

@router.get("/settings", response_model=Dict[str, ConfigurationItem])
async def get_system_settings(
    category: Optional[ConfigCategory] = None,
    current_user: User = Depends(require_admin_role),
    db = Depends(get_database)
):
    """Get system settings, optionally filtered by category"""
    
    config = await get_or_create_system_config(db)
    settings = config.get("settings", {})
    
    if category:
        filtered_settings = {
            key: setting for key, setting in settings.items()
            if setting.get("category") == category
        }
        return filtered_settings
    
    return settings

@router.get("/settings/{setting_key}", response_model=ConfigurationItem)
async def get_setting(
    setting_key: str,
    current_user: User = Depends(require_admin_role),
    db = Depends(get_database)
):
    """Get a specific system setting"""
    
    config = await get_or_create_system_config(db)
    settings = config.get("settings", {})
    
    if setting_key not in settings:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Setting '{setting_key}' not found"
        )
    
    return settings[setting_key]

@router.put("/settings/{setting_key}")
async def update_setting(
    setting_key: str,
    update_data: ConfigurationUpdate,
    current_user: User = Depends(require_admin_role),
    db = Depends(get_database)
):
    """Update a system setting"""
    
    config = await get_or_create_system_config(db)
    settings = config.get("settings", {})
    
    if setting_key not in settings:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Setting '{setting_key}' not found"
        )
    
    setting = settings[setting_key]
    old_value = setting["value"]
    
    # Validate new value based on data type and rules
    validation_error = validate_setting_value(setting, update_data.value)
    if validation_error:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=validation_error
        )
    
    # Update setting
    await db.system_config.update_one(
        {},
        {
            "$set": {
                f"settings.{setting_key}.value": update_data.value,
                "updated_at": datetime.utcnow(),
                "updated_by": current_user.username
            }
        }
    )
    
    # Log configuration change
    await log_config_change(
        db, current_user, setting_key,
        old_value, update_data.value, update_data.reason, setting["category"]
    )
    
    # Log audit event
    await log_audit_event(
        db, str(current_user.id), current_user.username, AuditAction.UPDATE,
        "system_config", setting_key,
        {"old_value": old_value, "new_value": update_data.value, "reason": update_data.reason},
        None
    )
    
    return {
        "message": f"Setting '{setting_key}' updated successfully",
        "setting_key": setting_key,
        "old_value": old_value,
        "new_value": update_data.value,
        "requires_restart": setting.get("requires_restart", False)
    }

def validate_setting_value(setting: dict, value: Any) -> Optional[str]:
    """Validate a setting value against its rules"""
    
    data_type = setting.get("data_type", "string")
    validation_rules = setting.get("validation_rules", {})
    
    # Type validation
    if data_type == "integer" and not isinstance(value, int):
        return f"Value must be an integer"
    elif data_type == "boolean" and not isinstance(value, bool):
        return f"Value must be a boolean"
    elif data_type == "string" and not isinstance(value, str):
        return f"Value must be a string"
    
    # Range validation for integers
    if data_type == "integer" and validation_rules:
        if "min" in validation_rules and value < validation_rules["min"]:
            return f"Value must be at least {validation_rules['min']}"
        if "max" in validation_rules and value > validation_rules["max"]:
            return f"Value must be at most {validation_rules['max']}"
    
    return None

async def log_config_change(
    db, current_user: User, config_key: str, old_value: Any,
    new_value: Any, reason: Optional[str], category: ConfigCategory
):
    """Log a configuration change to history"""

    history_entry = {
        "_id": ObjectId(),
        "config_key": config_key,
        "old_value": old_value,
        "new_value": new_value,
        "changed_by": current_user.username,
        "changed_at": datetime.utcnow(),
        "reason": reason,
        "category": category
    }

    await db.config_history.insert_one(history_entry)

@router.get("/history", response_model=List[ConfigurationHistory])
async def get_configuration_history(
    limit: int = 50,
    category: Optional[ConfigCategory] = None,
    config_key: Optional[str] = None,
    current_user: User = Depends(require_admin_role),
    db = Depends(get_database)
):
    """Get configuration change history"""

    query = {}
    if category:
        query["category"] = category
    if config_key:
        query["config_key"] = config_key

    cursor = db.config_history.find(query).sort("changed_at", -1).limit(limit)
    history = await cursor.to_list(length=limit)

    # Convert ObjectId to string and format dates
    for entry in history:
        entry["id"] = str(entry["_id"])
        entry.pop("_id", None)

        # Convert datetime fields to ISO format strings if they exist
        if "changed_at" in entry and entry["changed_at"] is not None:
            entry["changed_at"] = entry["changed_at"].isoformat() if hasattr(entry["changed_at"], 'isoformat') else entry["changed_at"]

    return history

@router.post("/reset-to-defaults")
async def reset_to_defaults(
    confirm: bool = False,
    current_user: User = Depends(require_admin_role),
    db = Depends(get_database)
):
    """Reset system configuration to defaults"""

    if not confirm:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Must confirm reset by setting confirm=true"
        )

    # Get current config for backup
    current_config = await get_or_create_system_config(db)

    # Reset to defaults
    default_config = {
        "modules": DEFAULT_MODULES,
        "settings": DEFAULT_SETTINGS,
        "updated_at": datetime.utcnow(),
        "updated_by": current_user.username,
        "version": "1.0.0"
    }

    await db.system_config.update_one(
        {},
        {"$set": default_config}
    )

    # Log the reset
    await log_audit_event(
        db, str(current_user.id), current_user.username, AuditAction.UPDATE,
        "system_config", "full_reset",
        {"action": "reset_to_defaults"},
        None
    )

    return {
        "message": "System configuration reset to defaults",
        "reset_by": current_user.username,
        "reset_at": datetime.utcnow().isoformat()
    }
