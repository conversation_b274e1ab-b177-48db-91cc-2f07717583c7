from fastapi import APIRouter, Depends, Query, Response, HTTPException
from typing import Optional, List
from datetime import datetime, timedelta
from app.models.user import User
from app.models.asset import Asset
from app.core.database import get_database
from app.routers.auth import get_current_user
from app.services.pdf_service import PDFReportGenerator
import csv
import io

router = APIRouter()

@router.get("/")
async def get_available_reports(
    current_user: User = Depends(get_current_user)
):
    """Get list of available reports"""
    return [
        {
            "_id": "asset-register",
            "name": "Asset Register",
            "type": "asset-register",
            "description": "Complete list of all assets with their details",
            "status": "available",
            "endpoint": "/api/reports/asset-register"
        },
        {
            "_id": "exceptions",
            "name": "Exception Report",
            "type": "exceptions",
            "description": "Assets with verification exceptions or issues",
            "status": "available",
            "endpoint": "/api/reports/exceptions"
        },
        {
            "_id": "depreciation-summary",
            "name": "Depreciation Summary",
            "type": "depreciation-summary",
            "description": "Summary of asset depreciation calculations",
            "status": "available",
            "endpoint": "/api/reports/depreciation-summary"
        },
        {
            "_id": "verification-results",
            "name": "Verification Results",
            "type": "verification-results",
            "description": "Results from recent verification campaigns",
            "status": "available",
            "endpoint": "/api/reports/verification-results"
        }
    ]

def asset_helper(asset) -> dict:
    """Helper function to convert MongoDB document to dict"""
    if asset:
        asset["_id"] = str(asset["_id"])
        return asset
    return None

@router.get("/asset-register")
async def get_asset_register(
    format: str = Query("pdf", description="Output format: pdf, json or csv"),
    location: Optional[str] = Query(None, description="Filter by location"),
    custodian: Optional[str] = Query(None, description="Filter by custodian"),
    depreciation_class: Optional[str] = Query(None, description="Filter by depreciation class"),
    status: Optional[str] = Query(None, description="Filter by status"),
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Get asset register report"""

    # Build filter query
    filter_query = {}
    if location:
        filter_query["location"] = {"$regex": location, "$options": "i"}
    if custodian:
        filter_query["custodian"] = {"$regex": custodian, "$options": "i"}
    if depreciation_class:
        filter_query["depreciation_class"] = depreciation_class
    if status:
        filter_query["status"] = status

    # Get assets
    cursor = db.assets.find(filter_query).sort("asset_id", 1)
    assets = await cursor.to_list(length=None)

    asset_list = [Asset(**asset_helper(asset)) for asset in assets]

    if format.lower() == "pdf":
        # Generate PDF
        pdf_generator = PDFReportGenerator()

        # Convert assets to dict format for PDF generation
        assets_dict = []
        for asset in asset_list:
            asset_dict = {
                'asset_id': asset.asset_id,
                'description': asset.description,
                'location': asset.location,
                'custodian': asset.custodian,
                'status': asset.status,
                'purchase_date': asset.purchase_date.isoformat() if asset.purchase_date else '',
                'purchase_cost': asset.purchase_cost,
                'current_value': asset.current_value or asset.purchase_cost,
                'category': asset.category or ''
            }
            assets_dict.append(asset_dict)

        # Prepare filters for PDF
        filters = {
            'location': location,
            'custodian': custodian,
            'depreciation_class': depreciation_class,
            'status': status
        }

        pdf_content = pdf_generator.generate_asset_register_pdf(assets_dict, filters)

        return Response(
            content=pdf_content,
            media_type="application/pdf",
            headers={"Content-Disposition": "attachment; filename=asset_register.pdf"}
        )

    elif format.lower() == "csv":
        # Generate CSV
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow([
            "Asset ID", "Description", "Location", "Custodian", "Status",
            "Purchase Date", "Purchase Cost", "Current Value", "Depreciation Class",
            "Serial Number", "Manufacturer", "Model", "Category"
        ])

        # Write data
        for asset in asset_list:
            writer.writerow([
                asset.asset_id, asset.description, asset.location, asset.custodian,
                asset.status, asset.purchase_date.strftime("%Y-%m-%d") if asset.purchase_date else "",
                asset.purchase_cost, asset.current_value or asset.purchase_cost,
                asset.depreciation_class, asset.serial_number or "", asset.manufacturer or "",
                asset.model or "", asset.category or ""
            ])

        csv_content = output.getvalue()
        output.close()

        return Response(
            content=csv_content,
            media_type="text/csv",
            headers={"Content-Disposition": "attachment; filename=asset_register.csv"}
        )

    return {
        "assets": asset_list,
        "total_count": len(asset_list),
        "filters_applied": {
            "location": location,
            "custodian": custodian,
            "depreciation_class": depreciation_class,
            "status": status
        }
    }

@router.get("/exceptions")
async def get_exception_report(
    format: str = Query("pdf", description="Output format: pdf, json or csv"),
    start_date: Optional[datetime] = Query(None, description="Filter from date"),
    end_date: Optional[datetime] = Query(None, description="Filter to date"),
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Get exception report - assets with verification issues"""

    # Build date filter for verification events
    date_filter = {}
    if start_date or end_date:
        if start_date:
            date_filter["$gte"] = start_date
        if end_date:
            date_filter["$lte"] = end_date

    # Find verification events with exceptions
    exception_filter = {
        "status": {"$in": ["not_found", "damaged", "exception"]}
    }
    if date_filter:
        exception_filter["verification_date"] = date_filter

    cursor = db.verification_events.find(exception_filter).sort("verification_date", -1)
    exception_events = await cursor.to_list(length=None)

    # Get asset details for each exception
    exceptions_with_assets = []
    for event in exception_events:
        asset = await db.assets.find_one({"asset_id": event["asset_id"]})
        if asset:
            exceptions_with_assets.append({
                "asset": Asset(**asset_helper(asset)),
                "verification_event": {
                    "verification_date": event["verification_date"],
                    "status": event["status"],
                    "verifier_name": event["verifier_name"],
                    "condition_notes": event.get("condition_notes"),
                    "exception_reason": event.get("exception_reason"),
                    "location_found": event.get("location_found")
                }
            })

    if format.lower() == "pdf":
        # Generate PDF
        pdf_generator = PDFReportGenerator()

        # Convert exceptions to dict format for PDF generation
        exceptions_dict = []
        for exc in exceptions_with_assets:
            asset = exc["asset"]
            event = exc["verification_event"]
            exception_dict = {
                'asset_id': asset.asset_id,
                'description': asset.description,
                'location': asset.location,
                'status': event["status"],
                'verification_date': event["verification_date"].isoformat() if event["verification_date"] else '',
                'verifier_name': event["verifier_name"],
                'notes': event.get("condition_notes", "") or event.get("exception_reason", "")
            }
            exceptions_dict.append(exception_dict)

        pdf_content = pdf_generator.generate_exception_report_pdf(exceptions_dict)

        return Response(
            content=pdf_content,
            media_type="application/pdf",
            headers={"Content-Disposition": "attachment; filename=exception_report.pdf"}
        )

    elif format.lower() == "csv":
        # Generate CSV
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow([
            'Asset ID', 'Description', 'Location', 'Exception Status',
            'Verification Date', 'Verifier Name', 'Notes', 'Exception Reason'
        ])

        # Write data rows
        for exc in exceptions_with_assets:
            asset = exc["asset"]
            event = exc["verification_event"]
            writer.writerow([
                asset.asset_id or '',
                asset.description or '',
                asset.location or '',
                event["status"] or '',
                event["verification_date"].strftime('%Y-%m-%d %H:%M:%S') if event["verification_date"] else '',
                event["verifier_name"] or '',
                event.get("condition_notes", "") or '',
                event.get("exception_reason", "") or ''
            ])

        csv_content = output.getvalue()
        output.close()

        return Response(
            content=csv_content,
            media_type="text/csv",
            headers={"Content-Disposition": "attachment; filename=exception_report.csv"}
        )

    return {
        "exceptions": exceptions_with_assets,
        "total_exceptions": len(exceptions_with_assets),
        "period": {
            "start_date": start_date,
            "end_date": end_date
        }
    }

@router.get("/depreciation-summary")
async def get_depreciation_summary_report(
    format: str = Query("pdf", description="Output format: pdf, json or csv"),
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Get depreciation summary report"""

    # Aggregate depreciation data by class
    pipeline = [
        {
            "$group": {
                "_id": "$depreciation_class",
                "total_purchase_cost": {"$sum": "$purchase_cost"},
                "total_current_value": {"$sum": {"$ifNull": ["$current_value", "$purchase_cost"]}},
                "total_accumulated_depreciation": {"$sum": {"$ifNull": ["$accumulated_depreciation", 0]}},
                "asset_count": {"$sum": 1},
                "avg_age_years": {
                    "$avg": {
                        "$divide": [
                            {"$subtract": [datetime.utcnow(), "$purchase_date"]},
                            365.25 * 24 * 60 * 60 * 1000  # Convert to years
                        ]
                    }
                }
            }
        }
    ]

    cursor = db.assets.aggregate(pipeline)
    summary_by_class = await cursor.to_list(length=None)

    # Calculate totals
    totals = {
        "total_purchase_cost": 0,
        "total_current_value": 0,
        "total_accumulated_depreciation": 0,
        "total_assets": 0
    }

    for item in summary_by_class:
        totals["total_purchase_cost"] += item["total_purchase_cost"]
        totals["total_current_value"] += item["total_current_value"]
        totals["total_accumulated_depreciation"] += item["total_accumulated_depreciation"]
        totals["total_assets"] += item["asset_count"]

    # Calculate depreciation percentage
    if totals["total_purchase_cost"] > 0:
        totals["depreciation_percentage"] = round(
            (totals["total_accumulated_depreciation"] / totals["total_purchase_cost"]) * 100, 2
        )
    else:
        totals["depreciation_percentage"] = 0

    if format.lower() == "pdf":
        # Generate PDF
        pdf_generator = PDFReportGenerator()

        summary_data = {
            "summary_by_class": summary_by_class,
            "totals": totals
        }

        pdf_content = pdf_generator.generate_depreciation_summary_pdf(summary_data)

        return Response(
            content=pdf_content,
            media_type="application/pdf",
            headers={"Content-Disposition": "attachment; filename=depreciation_summary.pdf"}
        )

    elif format.lower() == "csv":
        # Generate CSV
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow([
            'Depreciation Class', 'Asset Count', 'Total Purchase Cost',
            'Total Current Value', 'Total Accumulated Depreciation', 'Average Age (Years)'
        ])

        # Write data rows
        for item in summary_by_class:
            writer.writerow([
                item["_id"] or 'Unclassified',
                item["asset_count"],
                f"{item['total_purchase_cost']:.2f}",
                f"{item['total_current_value']:.2f}",
                f"{item['total_accumulated_depreciation']:.2f}",
                f"{item.get('avg_age_years', 0):.1f}"
            ])

        # Write totals row
        writer.writerow([])  # Empty row
        writer.writerow([
            'TOTALS',
            totals["total_assets"],
            f"{totals['total_purchase_cost']:.2f}",
            f"{totals['total_current_value']:.2f}",
            f"{totals['total_accumulated_depreciation']:.2f}",
            f"{totals.get('depreciation_percentage', 0):.1f}%"
        ])

        csv_content = output.getvalue()
        output.close()

        return Response(
            content=csv_content,
            media_type="text/csv",
            headers={"Content-Disposition": "attachment; filename=depreciation_summary.csv"}
        )

    return {
        "summary_by_class": summary_by_class,
        "totals": totals,
        "generated_at": datetime.utcnow()
    }

@router.get("/verification-results")
async def get_verification_results_report(
    format: str = Query("pdf", description="Output format: pdf, json or csv"),
    start_date: Optional[datetime] = Query(None, description="Filter from date"),
    end_date: Optional[datetime] = Query(None, description="Filter to date"),
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Get verification results report"""

    # Default to last 30 days if no dates provided
    if not start_date and not end_date:
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=30)

    # Build date filter
    date_filter = {}
    if start_date:
        date_filter["$gte"] = start_date
    if end_date:
        date_filter["$lte"] = end_date

    match_stage = {"verification_date": date_filter} if date_filter else {}

    # Aggregate verification statistics
    pipeline = [
        {"$match": match_stage},
        {
            "$group": {
                "_id": "$status",
                "count": {"$sum": 1}
            }
        }
    ]

    cursor = db.verification_events.aggregate(pipeline)
    status_counts = await cursor.to_list(length=None)

    # Get verification events by verifier
    verifier_pipeline = [
        {"$match": match_stage},
        {
            "$group": {
                "_id": "$verifier_name",
                "verifications_count": {"$sum": 1},
                "found_count": {
                    "$sum": {"$cond": [{"$eq": ["$status", "found"]}, 1, 0]}
                },
                "not_found_count": {
                    "$sum": {"$cond": [{"$eq": ["$status", "not_found"]}, 1, 0]}
                },
                "exception_count": {
                    "$sum": {"$cond": [{"$in": ["$status", ["damaged", "exception"]]}, 1, 0]}
                }
            }
        },
        {"$sort": {"verifications_count": -1}}
    ]

    verifier_cursor = db.verification_events.aggregate(verifier_pipeline)
    verifier_stats = await verifier_cursor.to_list(length=None)

    # Convert status counts to dictionary
    status_breakdown = {item["_id"]: item["count"] for item in status_counts}
    total_verifications = sum(status_breakdown.values())

    verification_data = {
        "period": {
            "start_date": start_date,
            "end_date": end_date
        },
        "summary": {
            "total_verifications": total_verifications,
            "status_breakdown": status_breakdown,
            "success_rate": round((status_breakdown.get("found", 0) / total_verifications * 100) if total_verifications > 0 else 0, 2)
        },
        "by_verifier": verifier_stats
    }

    if format.lower() == "pdf":
        # Generate PDF
        pdf_generator = PDFReportGenerator()
        pdf_content = pdf_generator.generate_verification_results_pdf(verification_data)

        return Response(
            content=pdf_content,
            media_type="application/pdf",
            headers={"Content-Disposition": "attachment; filename=verification_results.pdf"}
        )

    elif format.lower() == "csv":
        # Generate CSV
        output = io.StringIO()
        writer = csv.writer(output)

        # Write summary section
        writer.writerow(['Verification Results Summary'])
        writer.writerow(['Period', f"{start_date.strftime('%Y-%m-%d') if start_date else 'All time'} to {end_date.strftime('%Y-%m-%d') if end_date else 'Present'}"])
        writer.writerow(['Total Verifications', verification_data["summary"]["total_verifications"]])
        writer.writerow(['Success Rate', f"{verification_data['summary']['success_rate']}%"])
        writer.writerow([])  # Empty row

        # Write status breakdown
        writer.writerow(['Status Breakdown'])
        writer.writerow(['Status', 'Count'])
        for status, count in verification_data["summary"]["status_breakdown"].items():
            writer.writerow([status.replace('_', ' ').title(), count])
        writer.writerow([])  # Empty row

        # Write verifier statistics
        writer.writerow(['Verifier Statistics'])
        writer.writerow(['Verifier Name', 'Total Verifications', 'Found', 'Not Found', 'Exceptions'])
        for verifier in verification_data["by_verifier"]:
            writer.writerow([
                verifier["_id"],
                verifier["verifications_count"],
                verifier["found_count"],
                verifier["not_found_count"],
                verifier["exception_count"]
            ])

        csv_content = output.getvalue()
        output.close()

        return Response(
            content=csv_content,
            media_type="text/csv",
            headers={"Content-Disposition": "attachment; filename=verification_results.csv"}
        )

    return verification_data

@router.post("/custom")
async def create_custom_report(
    report_config: dict,
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Create a custom report based on user configuration"""

    try:
        # Extract configuration
        report_name = report_config.get("name", "Custom Report")
        filters = report_config.get("filters", {})
        columns = report_config.get("columns", [])
        format_type = report_config.get("format", "pdf")

        # Build MongoDB query from filters
        query = {}

        # Handle different filter types
        if filters.get("location"):
            query["location"] = {"$regex": filters["location"], "$options": "i"}
        if filters.get("custodian"):
            query["custodian"] = {"$regex": filters["custodian"], "$options": "i"}
        if filters.get("status"):
            query["status"] = filters["status"]
        if filters.get("category"):
            query["category"] = {"$regex": filters["category"], "$options": "i"}
        if filters.get("date_range"):
            date_range = filters["date_range"]
            if date_range.get("start") or date_range.get("end"):
                date_filter = {}
                if date_range.get("start"):
                    date_filter["$gte"] = datetime.fromisoformat(date_range["start"])
                if date_range.get("end"):
                    date_filter["$lte"] = datetime.fromisoformat(date_range["end"])
                query["purchase_date"] = date_filter

        # Get assets based on query
        cursor = db.assets.find(query).sort("asset_id", 1)
        assets = await cursor.to_list(length=None)

        # Convert to asset objects
        asset_list = [Asset(**asset_helper(asset)) for asset in assets]

        if format_type.lower() == "pdf":
            # Generate custom PDF
            pdf_generator = PDFReportGenerator()

            # Convert assets to dict format
            assets_dict = []
            for asset in asset_list:
                asset_dict = {
                    'asset_id': asset.asset_id,
                    'description': asset.description,
                    'location': asset.location,
                    'custodian': asset.custodian,
                    'status': asset.status,
                    'purchase_date': asset.purchase_date.isoformat() if asset.purchase_date else '',
                    'purchase_cost': asset.purchase_cost,
                    'current_value': asset.current_value or asset.purchase_cost,
                    'category': asset.category or ''
                }
                assets_dict.append(asset_dict)

            pdf_content = pdf_generator.generate_asset_register_pdf(assets_dict, filters)

            filename = f"{report_name.lower().replace(' ', '_')}.pdf"
            return Response(
                content=pdf_content,
                media_type="application/pdf",
                headers={"Content-Disposition": f"attachment; filename={filename}"}
            )

        # Return JSON format
        return {
            "report_name": report_name,
            "assets": asset_list,
            "total_count": len(asset_list),
            "filters_applied": filters,
            "columns": columns,
            "generated_at": datetime.utcnow()
        }

    except Exception as e:
        return {"error": f"Failed to generate custom report: {str(e)}"}

@router.post("/generate/{report_type}")
async def generate_report(
    report_type: str,
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Generate a report of the specified type"""

    # Map report types to their respective endpoints
    report_generators = {
        "asset-register": get_asset_register,
        "exceptions": get_exception_report,
        "depreciation-summary": get_depreciation_summary_report,
        "verification-results": get_verification_results_report
    }

    if report_type not in report_generators:
        return {"error": "Invalid report type"}

    # For now, just return success - in a real implementation,
    # this would generate and store the report
    return {
        "message": f"Report {report_type} generated successfully",
        "report_id": f"{report_type}-{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "status": "completed"
    }

@router.get("/download/{report_id}")
async def download_report(
    report_id: str,
    current_user: User = Depends(get_current_user)
):
    """Download a generated report"""

    # For now, return a simple message - in a real implementation,
    # this would return the actual file
    return {
        "message": f"Download functionality for report {report_id} not yet implemented",
        "report_id": report_id
    }
