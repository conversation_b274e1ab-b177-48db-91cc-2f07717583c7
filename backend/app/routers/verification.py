from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from app.models.verification import VerificationEvent, VerificationEventCreate, VerificationCampaign
from app.models.user import User
from app.models.audit import AuditAction
from app.core.database import get_database
from app.routers.auth import get_current_user
from app.services.audit_service import log_audit_event
from bson import ObjectId
from pydantic import BaseModel

router = APIRouter()

class VerificationRequest(BaseModel):
    method: str  # 'barcode' or 'photo'
    asset_id: str = None
    location: str = None
    notes: str = None

class AssetVerificationRequest(BaseModel):
    status: str = "verified"
    verification_date: str = None
    notes: str = None

def verification_helper(verification) -> dict:
    """Helper function to convert MongoDB document to dict"""
    if verification:
        verification["id"] = str(verification["_id"])
        return verification
    return None

@router.post("/events", response_model=VerificationEvent, status_code=status.HTTP_201_CREATED)
async def create_verification_event(
    event: VerificationEventCreate,
    request: Request,
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Create a new verification event"""

    # Check if asset exists
    asset = await db.assets.find_one({"asset_id": event.asset_id})
    if not asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )

    # Create verification event
    event_dict = event.dict()
    event_dict.update({
        "_id": ObjectId(),
        "verifier_id": str(current_user.id),
        "verifier_name": current_user.full_name,
        "created_at": datetime.utcnow()
    })

    # Insert verification event
    result = await db.verification_events.insert_one(event_dict)

    # Update asset location if found in different location
    if event.status == "found" and event.location_found and event.location_found != asset["location"]:
        await db.assets.update_one(
            {"asset_id": event.asset_id},
            {
                "$set": {
                    "location": event.location_found,
                    "updated_at": datetime.utcnow(),
                    "updated_by": current_user.username
                }
            }
        )

    # Log audit event
    await log_audit_event(
        db, str(current_user.id), current_user.username, AuditAction.VERIFY,
        "asset", event.asset_id,
        {
            "verification_status": event.status,
            "location_found": event.location_found,
            "condition_notes": event.condition_notes
        },
        request.client.host if request.client else None
    )

    # Retrieve and return the created event
    created_event = await db.verification_events.find_one({"_id": result.inserted_id})
    return VerificationEvent(**verification_helper(created_event))

@router.get("/events", response_model=List[VerificationEvent])
async def get_verification_events(
    asset_id: Optional[str] = Query(None, description="Filter by asset ID"),
    verifier_id: Optional[str] = Query(None, description="Filter by verifier ID"),
    status: Optional[str] = Query(None, description="Filter by verification status"),
    start_date: Optional[datetime] = Query(None, description="Filter from date"),
    end_date: Optional[datetime] = Query(None, description="Filter to date"),
    skip: int = Query(0, ge=0, description="Number of events to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of events to return"),
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Get verification events with filtering"""

    # Build filter query
    filter_query = {}

    if asset_id:
        filter_query["asset_id"] = asset_id

    if verifier_id:
        filter_query["verifier_id"] = verifier_id

    if status:
        filter_query["status"] = status

    if start_date or end_date:
        date_filter = {}
        if start_date:
            date_filter["$gte"] = start_date
        if end_date:
            date_filter["$lte"] = end_date
        filter_query["verification_date"] = date_filter

    # Get verification events
    cursor = db.verification_events.find(filter_query).skip(skip).limit(limit).sort("verification_date", -1)
    events = await cursor.to_list(length=limit)

    return [VerificationEvent(**verification_helper(event)) for event in events]

@router.get("/events/{event_id}", response_model=VerificationEvent)
async def get_verification_event(
    event_id: str,
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Get a specific verification event"""

    try:
        event = await db.verification_events.find_one({"_id": ObjectId(event_id)})
    except:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid event ID"
        )

    if not event:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Verification event not found"
        )

    return VerificationEvent(**verification_helper(event))

@router.get("/statistics")
async def get_verification_statistics(
    start_date: Optional[datetime] = Query(None, description="Statistics from date"),
    end_date: Optional[datetime] = Query(None, description="Statistics to date"),
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Get verification statistics"""

    # Build date filter
    date_filter = {}
    if start_date or end_date:
        if start_date:
            date_filter["$gte"] = start_date
        if end_date:
            date_filter["$lte"] = end_date

    match_stage = {"verification_date": date_filter} if date_filter else {}

    # Aggregate verification statistics
    pipeline = [
        {"$match": match_stage},
        {
            "$group": {
                "_id": "$status",
                "count": {"$sum": 1}
            }
        }
    ]

    cursor = db.verification_events.aggregate(pipeline)
    status_counts = await cursor.to_list(length=None)

    # Convert to dictionary
    stats = {item["_id"]: item["count"] for item in status_counts}

    # Calculate totals
    total_verifications = sum(stats.values())

    return {
        "status_breakdown": stats,
        "total_verifications": total_verifications,
        "period": {
            "start_date": start_date,
            "end_date": end_date
        }
    }

@router.get("/stats")
async def get_verification_stats(
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
) -> Dict[str, Any]:
    """Get verification statistics for dashboard"""

    # Get total verified assets (verified in last 30 days)
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    total_verified = await db.assets.count_documents({
        "last_verification": {"$gte": thirty_days_ago}
    })

    # Get pending verification count
    pending_verification = await db.assets.count_documents({
        "$or": [
            {"last_verification": {"$lt": thirty_days_ago}},
            {"last_verification": {"$exists": False}}
        ]
    })

    # Get verification exceptions
    verification_exceptions = await db.verification_events.count_documents({
        "status": {"$in": ["missing", "damaged", "exception"]},
        "verification_date": {"$gte": thirty_days_ago}
    })

    # Calculate verification progress
    total_assets = await db.assets.count_documents({})
    verification_progress = (total_verified / total_assets * 100) if total_assets > 0 else 0

    # Get last verification date
    last_verification_cursor = db.assets.find(
        {"last_verification": {"$exists": True}},
        {"last_verification": 1}
    ).sort("last_verification", -1).limit(1)

    last_verification_result = await last_verification_cursor.to_list(1)
    last_verification_date = None
    if last_verification_result:
        last_verification_date = last_verification_result[0]["last_verification"].isoformat()

    return {
        "total_verified": total_verified,
        "pending_verification": pending_verification,
        "verification_exceptions": verification_exceptions,
        "verification_progress": verification_progress,
        "last_verification_date": last_verification_date
    }

@router.post("/start")
async def start_verification(
    request: VerificationRequest,
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Start a verification session"""

    # Create verification session
    verification_session = {
        "user_id": str(current_user.id),
        "method": request.method,
        "started_at": datetime.utcnow(),
        "status": "active"
    }

    result = await db.verification_sessions.insert_one(verification_session)

    # Log the action
    await log_audit_event(
        db, str(current_user.id), current_user.username, AuditAction.CREATE,
        "verification_session", str(result.inserted_id),
        {"method": request.method},
        None
    )

    return {
        "session_id": str(result.inserted_id),
        "message": f"{request.method.title()} verification started successfully"
    }

@router.post("/verify/{asset_id}")
async def verify_asset(
    asset_id: str,
    verification_data: AssetVerificationRequest,
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Verify a specific asset"""

    # Check if asset exists
    asset = await db.assets.find_one({"$or": [{"asset_id": asset_id}, {"asset_tag": asset_id}]})
    if not asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )

    # Create verification event
    verification_event = {
        "_id": ObjectId(),
        "asset_id": asset.get("asset_id", asset.get("asset_tag")),
        "verifier_id": str(current_user.id),
        "verifier_name": current_user.full_name,
        "verification_date": verification_data.verification_date or datetime.now().isoformat(),
        "status": verification_data.status,
        "method": "manual",
        "location": asset.get("location"),
        "notes": verification_data.notes or "Manual verification",
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }

    # Insert verification event
    result = await db.verification_events.insert_one(verification_event)

    # Update asset verification status
    await db.assets.update_one(
        {"_id": asset["_id"]},
        {
            "$set": {
                "last_verification_date": verification_event["verification_date"],
                "verification_status": verification_data.status,
                "updated_at": datetime.now().isoformat()
            }
        }
    )

    # Log the action
    await log_audit_event(
        db, str(current_user.id), current_user.username, AuditAction.UPDATE,
        "asset", str(asset["_id"]),
        {"verification_status": verification_data.status},
        None
    )

    return {
        "message": "Asset verified successfully",
        "verification_id": str(result.inserted_id),
        "asset_id": asset.get("asset_id", asset.get("asset_tag")),
        "status": verification_data.status
    }
