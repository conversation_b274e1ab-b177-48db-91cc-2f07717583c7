from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File, Request
from typing import List, Optional
from datetime import datetime, timedelta
from bson import ObjectId
from app.models.asset import Asset, AssetCreate, AssetUpdate, CustodianAssignmentRequest, BulkCustodianAssignmentRequest, CustodianAssignment
from app.models.user import User, UserRole
from app.models.audit import AuditAction
from app.models.wip import WIPTracking, ProjectStatus
from app.core.database import get_database
from app.routers.auth import get_current_user
from app.routers.users import require_admin_or_auditor_role
from app.services.notification_service import NotificationService

def require_write_access(current_user: User = Depends(get_current_user)):
    """Dependency to ensure only admin and asset_manager users can modify assets"""
    if current_user.role not in [UserRole.ADMIN, UserRole.ASSET_MANAGER]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin or Asset Manager role required for this operation"
        )
    return current_user

def require_asset_manager_or_admin(current_user: User = Depends(get_current_user)):
    """Require Asset Manager or Admin role"""
    if current_user.role not in [UserRole.ADMIN, UserRole.ASSET_MANAGER]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Asset Manager or Admin role required"
        )
    return current_user

async def check_asset_deletion_allowed(asset_id: str, db) -> bool:
    """
    Check if an asset can be deleted (not in WIP or under verification)

    Args:
        asset_id: Asset ID to check
        db: Database connection

    Returns:
        bool: True if deletion is allowed
    """
    # Check if asset is in WIP
    wip_record = await db.wip_tracking.find_one({
        "asset_id": asset_id,
        "project_status": {"$in": ["planning", "in_progress", "on_hold", "ready_for_capitalization"]}
    })

    if wip_record:
        return False

    # Check if asset is under verification
    asset = await db.assets.find_one({"asset_id": asset_id})
    if asset and asset.get("status") == "under_verification":
        return False

    # Check for active verification campaigns
    active_verification = await db.verification_events.find_one({
        "asset_id": asset_id,
        "verification_date": {"$gte": datetime.utcnow() - timedelta(days=1)},
        "status": {"$in": ["in_progress", "pending"]}
    })

    if active_verification:
        return False

    return True
from app.services.audit_service import log_audit_event
from bson import ObjectId
import uuid

router = APIRouter()

def clean_mongodb_document(doc):
    """Recursively clean MongoDB document by converting ObjectIds to strings"""
    if isinstance(doc, dict):
        cleaned = {}
        for key, value in doc.items():
            if key == "_id" and isinstance(value, ObjectId):
                cleaned[key] = str(value)
            elif isinstance(value, ObjectId):
                cleaned[key] = str(value)
            elif isinstance(value, dict):
                cleaned[key] = clean_mongodb_document(value)
            elif isinstance(value, list):
                cleaned[key] = [clean_mongodb_document(item) if isinstance(item, (dict, ObjectId)) else item for item in value]
            else:
                cleaned[key] = value
        return cleaned
    elif isinstance(doc, ObjectId):
        return str(doc)
    elif isinstance(doc, list):
        return [clean_mongodb_document(item) for item in doc]
    else:
        return doc

def asset_helper(asset) -> dict:
    """Helper function to convert MongoDB document to dict"""
    if asset:
        return clean_mongodb_document(asset)
    return None

@router.post("/", response_model=Asset, status_code=status.HTTP_201_CREATED)
async def create_asset(
    asset: AssetCreate,
    request: Request,
    current_user: User = Depends(require_write_access),
    db = Depends(get_database)
):
    """Create a new asset"""

    # Check permissions
    if current_user.role not in [UserRole.ADMIN, UserRole.ASSET_MANAGER]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to create assets"
        )

    # Check if asset_id already exists
    existing_asset = await db.assets.find_one({"asset_id": asset.asset_id})
    if existing_asset:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Asset ID already exists"
        )

    # Check if barcode already exists (if provided)
    if asset.barcode:
        existing_barcode = await db.assets.find_one({"barcode": asset.barcode})
        if existing_barcode:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Barcode already exists"
            )

    # Create asset document
    asset_dict = asset.dict()
    asset_dict.update({
        "_id": ObjectId(),
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
        "created_by": current_user.username,
        "updated_by": current_user.username,
        "photos": [],
        "current_value": asset.purchase_cost,  # Initial value equals purchase cost
        "accumulated_depreciation": 0.0
    })

    # Insert asset
    result = await db.assets.insert_one(asset_dict)

    # Log audit event
    await log_audit_event(
        db, str(current_user.id), current_user.username, AuditAction.CREATE,
        "asset", asset.asset_id,
        {"asset_data": asset.dict()},
        request.client.host if request.client else None
    )

    # Retrieve and return the created asset
    created_asset = await db.assets.find_one({"_id": result.inserted_id})
    return Asset(**asset_helper(created_asset))

@router.get("/")
async def get_assets(
    skip: int = Query(0, ge=0, description="Number of assets to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of assets to return"),
    location: Optional[str] = Query(None, description="Filter by location"),
    custodian: Optional[str] = Query(None, description="Filter by custodian"),
    status: Optional[str] = Query(None, description="Filter by status"),
    depreciation_class: Optional[str] = Query(None, description="Filter by depreciation class"),
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Get list of assets with optional filtering"""
    
    # Build filter query
    filter_query = {}
    if location:
        filter_query["location"] = {"$regex": location, "$options": "i"}
    if custodian:
        filter_query["custodian"] = {"$regex": custodian, "$options": "i"}
    if status:
        filter_query["status"] = status
    if depreciation_class:
        filter_query["depreciation_class"] = depreciation_class
    
    # Get assets
    cursor = db.assets.find(filter_query).skip(skip).limit(limit).sort("created_at", -1)
    assets = await cursor.to_list(length=limit)
    
    # Convert to frontend-compatible format
    frontend_assets = []
    for asset in assets:
        frontend_asset = {
            "_id": str(asset["_id"]),
            "asset_tag": asset.get("asset_id", ""),
            "name": asset.get("description", ""),
            "description": asset.get("description", ""),
            "category": asset.get("category", "Unknown"),
            "location": asset.get("location", ""),
            "department": asset.get("department", ""),
            "assigned_to": asset.get("custodian", ""),
            "purchase_date": asset.get("purchase_date", datetime.utcnow()).isoformat(),
            "purchase_cost": asset.get("purchase_cost", 0),
            "current_value": asset.get("current_value", asset.get("purchase_cost", 0)),
            "depreciation_method": asset.get("depreciation_method", "straight_line"),
            "useful_life_years": asset.get("useful_life_years", 5),
            "salvage_value": asset.get("salvage_value", 0),
            "status": asset.get("status", "active"),
            "condition": asset.get("condition", "good"),
            "warranty_expiry": asset.get("warranty_expiry", datetime.utcnow()).isoformat() if asset.get("warranty_expiry") else None,
            "last_maintenance": asset.get("last_maintenance", datetime.utcnow()).isoformat() if asset.get("last_maintenance") else None,
            "next_maintenance": asset.get("next_maintenance", datetime.utcnow()).isoformat() if asset.get("next_maintenance") else None,
            "barcode": asset.get("barcode", ""),
            "qr_code": asset.get("qr_code", ""),
            "photos": asset.get("photos", []),
            "documents": asset.get("documents", []),
            "created_at": asset.get("created_at", datetime.utcnow()).isoformat(),
            "updated_at": asset.get("updated_at", datetime.utcnow()).isoformat(),
            "created_by": asset.get("created_by", ""),
            "updated_by": asset.get("updated_by", "")
        }
        frontend_assets.append(frontend_asset)

    return frontend_assets

@router.get("/verification-status")
async def get_verification_status(
    current_user: User = Depends(require_asset_manager_or_admin),
    db = Depends(get_database)
):
    """Get real-time status of verification events for Asset Manager"""

    try:
        # Get all assets first
        cursor = db.assets.find({})
        assets = await cursor.to_list(length=None)

        # Clean all MongoDB documents
        assets_with_verification = [clean_mongodb_document(asset) for asset in assets]

        # Categorize assets
        active_verifications = []
        overdue_verifications = []
        upcoming_verifications = []
        exceptions = []

        for asset in assets_with_verification:
            # Convert datetime objects to strings for JSON serialization
            last_verification_date = asset.get("last_verification_date")
            if isinstance(last_verification_date, datetime):
                last_verification_date = last_verification_date.isoformat()

            next_verification_due = asset.get("next_verification_due")
            if isinstance(next_verification_due, datetime):
                next_verification_due = next_verification_due.isoformat()

            asset_data = {
                "asset_id": asset.get("asset_id"),
                "description": asset.get("description"),
                "location": asset.get("location"),
                "custodian": asset.get("custodian"),
                "custodians": asset.get("custodians", []),
                "status": asset.get("status"),
                "last_verification_date": last_verification_date,
                "next_verification_due": next_verification_due,
                "last_verification": None  # Simplified for now
            }

            # Check if verification is in progress
            if asset.get("status") == "under_verification":
                active_verifications.append(asset_data)

            # Check if overdue (simplified check)
            if next_verification_due and isinstance(asset.get("next_verification_due"), datetime):
                if asset["next_verification_due"] < datetime.utcnow():
                    overdue_verifications.append(asset_data)
                else:
                    # Check if verification is due soon (within 30 days)
                    days_until_due = (asset["next_verification_due"] - datetime.utcnow()).days
                    if 0 <= days_until_due <= 30:
                        upcoming_verifications.append(asset_data)

        return {
            "summary": {
                "total_assets": len(assets_with_verification),
                "active_verifications": len(active_verifications),
                "overdue_verifications": len(overdue_verifications),
                "upcoming_verifications": len(upcoming_verifications),
                "exceptions": len(exceptions)
            },
            "active_verifications": active_verifications,
            "overdue_verifications": overdue_verifications,
            "upcoming_verifications": upcoming_verifications,
            "exceptions": exceptions
        }

    except Exception as e:
        # Log the error and return a safe response
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error in get_verification_status: {str(e)}")

        # Return empty response structure
        return {
            "summary": {
                "total_assets": 0,
                "active_verifications": 0,
                "overdue_verifications": 0,
                "upcoming_verifications": 0,
                "exceptions": 0
            },
            "active_verifications": [],
            "overdue_verifications": [],
            "upcoming_verifications": [],
            "exceptions": []
        }

@router.get("/{asset_id}", response_model=Asset)
async def get_asset(
    asset_id: str,
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Get a specific asset by ID"""
    
    asset = await db.assets.find_one({"asset_id": asset_id})
    if not asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )
    
    return Asset(**asset_helper(asset))

@router.put("/{asset_id}", response_model=Asset)
async def update_asset(
    asset_id: str,
    asset_update: AssetUpdate,
    request: Request,
    current_user: User = Depends(require_write_access),
    db = Depends(get_database)
):
    """Update an existing asset"""

    # Check permissions
    if current_user.role not in [UserRole.ADMIN, UserRole.ASSET_MANAGER]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to update assets"
        )

    # Check if asset exists
    existing_asset = await db.assets.find_one({"asset_id": asset_id})
    if not existing_asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )

    # Prepare update data
    update_data = {k: v for k, v in asset_update.dict().items() if v is not None}
    if update_data:
        update_data["updated_at"] = datetime.utcnow()
        update_data["updated_by"] = current_user.username

        # Log what changed for audit
        changes = {}
        for key, new_value in update_data.items():
            if key in existing_asset and existing_asset[key] != new_value:
                changes[key] = {
                    "old_value": existing_asset[key],
                    "new_value": new_value
                }

        # Update asset
        await db.assets.update_one(
            {"asset_id": asset_id},
            {"$set": update_data}
        )

        # Log audit event
        await log_audit_event(
            db, str(current_user.id), current_user.username, AuditAction.UPDATE,
            "asset", asset_id,
            {"changes": changes},
            request.client.host if request.client else None
        )

    # Return updated asset
    updated_asset = await db.assets.find_one({"asset_id": asset_id})
    return Asset(**asset_helper(updated_asset))

@router.delete("/{asset_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_asset(
    asset_id: str,
    current_user: User = Depends(require_write_access),
    db = Depends(get_database)
):
    """Delete an asset - Asset Manager cannot delete assets in WIP or under verification"""

    # Check if asset exists
    existing_asset = await db.assets.find_one({"asset_id": asset_id})
    if not existing_asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )

    # Asset Manager role restrictions
    if current_user.role == UserRole.ASSET_MANAGER:
        # Check if asset can be deleted (not in WIP or under verification)
        deletion_allowed = await check_asset_deletion_allowed(asset_id, db)
        if not deletion_allowed:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot delete asset: Asset is in WIP or under verification"
            )

    # Log the deletion
    from app.services.audit_service import log_audit_event
    await log_audit_event(
        db, str(current_user.id), current_user.username, AuditAction.DELETE,
        "asset", asset_id,
        {"asset_description": existing_asset.get("description")},
        None
    )

    # Delete asset
    await db.assets.delete_one({"asset_id": asset_id})

@router.get("/barcode/{barcode}", response_model=Asset)
async def get_asset_by_barcode(
    barcode: str,
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Get asset by barcode"""
    
    asset = await db.assets.find_one({"barcode": barcode})
    if not asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset with this barcode not found"
        )
    
    return Asset(**asset_helper(asset))

@router.post("/{asset_id}/photos", status_code=status.HTTP_201_CREATED)
async def upload_asset_photo(
    asset_id: str,
    request: Request,
    current_user: User = Depends(require_write_access),
    db = Depends(get_database),
    file: UploadFile = File(...)
):
    """Upload a photo for an asset"""

    # Check if asset exists
    asset = await db.assets.find_one({"asset_id": asset_id})
    if not asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )

    # Validate file type
    if not file.content_type.startswith("image/"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File must be an image"
        )

    # Create uploads directory if it doesn't exist
    import os
    from app.core.config import settings
    upload_dir = settings.UPLOAD_DIR
    os.makedirs(upload_dir, exist_ok=True)

    # Generate unique filename
    import uuid
    file_extension = file.filename.split(".")[-1] if "." in file.filename else "jpg"
    filename = f"{asset_id}_{uuid.uuid4()}.{file_extension}"
    file_path = os.path.join(upload_dir, filename)

    # Save file
    with open(file_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)

    # Update asset with photo URL
    photo_url = f"/uploads/{filename}"
    await db.assets.update_one(
        {"asset_id": asset_id},
        {
            "$push": {"photos": photo_url},
            "$set": {"updated_at": datetime.utcnow(), "updated_by": current_user.username}
        }
    )

    # Log audit event
    await log_audit_event(
        db, str(current_user.id), current_user.username, AuditAction.PHOTO_UPLOAD,
        "asset", asset_id,
        {"photo_url": photo_url, "filename": file.filename},
        request.client.host if request.client else None
    )

    return {"message": "Photo uploaded successfully", "photo_url": photo_url}

@router.get("/search")
async def search_assets(
    q: str = Query(..., description="Search query"),
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Search assets by description, location, custodian, or asset ID"""

    search_filter = {
        "$or": [
            {"asset_id": {"$regex": q, "$options": "i"}},
            {"description": {"$regex": q, "$options": "i"}},
            {"location": {"$regex": q, "$options": "i"}},
            {"custodian": {"$regex": q, "$options": "i"}},
            {"manufacturer": {"$regex": q, "$options": "i"}},
            {"model": {"$regex": q, "$options": "i"}},
            {"serial_number": {"$regex": q, "$options": "i"}}
        ]
    }

    cursor = db.assets.find(search_filter).limit(50)
    assets = await cursor.to_list(length=50)

    return [Asset(**asset_helper(asset)) for asset in assets]

@router.post("/{asset_id}/custodians", status_code=status.HTTP_201_CREATED)
async def assign_custodians(
    asset_id: str,
    assignment_request: BulkCustodianAssignmentRequest,
    current_user: User = Depends(require_asset_manager_or_admin),
    db = Depends(get_database)
):
    """Assign one or more custodians to an asset"""

    # Check if asset exists
    existing_asset = await db.assets.find_one({"asset_id": asset_id})
    if not existing_asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )

    # Validate custodian users exist
    custodian_assignments = []
    for custodian_req in assignment_request.custodians:
        user = await db.users.find_one({"_id": ObjectId(custodian_req.user_id)})
        if not user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"User with ID {custodian_req.user_id} not found"
            )

        # Create custodian assignment
        custodian_assignment = CustodianAssignment(
            user_id=custodian_req.user_id,
            username=user["username"],
            full_name=user.get("full_name", user["username"]),
            email=user["email"],
            assigned_date=datetime.utcnow(),
            assigned_by=current_user.username,
            is_primary=custodian_req.is_primary,
            notification_sent=False
        )
        custodian_assignments.append(custodian_assignment)

    # Update asset with custodians
    update_data = {}
    if assignment_request.replace_existing:
        update_data["custodians"] = [c.dict() for c in custodian_assignments]
    else:
        # Add to existing custodians
        existing_custodians = existing_asset.get("custodians", [])
        # Remove duplicates by user_id
        existing_user_ids = {c.get("user_id") for c in existing_custodians}
        new_custodians = [c for c in custodian_assignments if c.user_id not in existing_user_ids]
        update_data["custodians"] = existing_custodians + [c.dict() for c in new_custodians]

    # Set primary custodian name for backward compatibility
    primary_custodian = next((c for c in custodian_assignments if c.is_primary), None)
    if primary_custodian:
        update_data["custodian"] = primary_custodian.full_name

    # Update asset
    await db.assets.update_one(
        {"asset_id": asset_id},
        {
            "$set": {
                **update_data,
                "updated_at": datetime.utcnow(),
                "updated_by": current_user.username
            }
        }
    )

    # Send notifications to custodians
    asset_obj = Asset(**asset_helper(existing_asset))
    for custodian in custodian_assignments:
        await NotificationService.send_custodian_assignment_notification(
            asset_obj, custodian, current_user, db
        )

    # Log the assignment
    from app.services.audit_service import log_audit_event
    await log_audit_event(
        db, str(current_user.id), current_user.username, AuditAction.UPDATE,
        "asset", asset_id,
        {
            "action": "custodian_assignment",
            "custodians": [{"user_id": c.user_id, "username": c.username, "is_primary": c.is_primary} for c in custodian_assignments],
            "replace_existing": assignment_request.replace_existing
        },
        None
    )

    return {
        "message": f"Successfully assigned {len(custodian_assignments)} custodian(s) to asset {asset_id}",
        "assigned_custodians": [
            {
                "user_id": c.user_id,
                "username": c.username,
                "full_name": c.full_name,
                "is_primary": c.is_primary
            } for c in custodian_assignments
        ]
    }

@router.get("/{asset_id}/depreciation")
async def get_asset_depreciation_summary(
    asset_id: str,
    current_user: User = Depends(require_asset_manager_or_admin),
    db = Depends(get_database)
):
    """Get depreciation summary for a specific asset - read-only but exportable"""

    # Check if asset exists
    asset = await db.assets.find_one({"asset_id": asset_id})
    if not asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )

    # Calculate current depreciation
    purchase_date = asset["purchase_date"]
    purchase_cost = asset["purchase_cost"]
    useful_life_years = asset["useful_life_years"]

    # Calculate age in years
    age_in_days = (datetime.utcnow() - purchase_date).days
    age_in_years = age_in_days / 365.25

    # Calculate depreciation (straight-line method)
    annual_depreciation = purchase_cost / useful_life_years
    accumulated_depreciation = min(annual_depreciation * age_in_years, purchase_cost)
    current_value = max(purchase_cost - accumulated_depreciation, 0)

    # Get depreciation rate
    depreciation_rate = (1 / useful_life_years) * 100  # Percentage per year

    # Get historical depreciation calculations if any
    depreciation_history = []
    cursor = db.depreciation_calculations.find({"asset_id": asset_id}).sort("calculation_date", -1)
    async for calc in cursor:
        depreciation_history.append({
            "calculation_date": calc["calculation_date"],
            "annual_depreciation": calc["annual_depreciation"],
            "accumulated_depreciation": calc["accumulated_depreciation"],
            "current_value": calc["current_value"],
            "calculated_by": calc["calculated_by"]
        })

    return {
        "asset_id": asset_id,
        "description": asset["description"],
        "purchase_date": purchase_date,
        "purchase_cost": purchase_cost,
        "useful_life_years": useful_life_years,
        "depreciation_class": asset["depreciation_class"],
        "current_calculations": {
            "age_in_years": round(age_in_years, 2),
            "depreciation_rate_percent": round(depreciation_rate, 2),
            "annual_depreciation": round(annual_depreciation, 2),
            "accumulated_depreciation": round(accumulated_depreciation, 2),
            "current_value": round(current_value, 2),
            "depreciation_method": "straight_line"
        },
        "depreciation_history": depreciation_history,
        "export_data": {
            "asset_value": round(current_value, 2),
            "depreciation_rate": round(depreciation_rate, 2),
            "accumulated_depreciation": round(accumulated_depreciation, 2),
            "remaining_useful_life_years": max(0, useful_life_years - age_in_years)
        }
    }
