from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File, Request
from typing import List, Optional
from datetime import datetime
from app.models.asset import Asset, AssetCreate, AssetUpdate
from app.models.user import User, UserRole
from app.models.audit import AuditAction
from app.core.database import get_database
from app.routers.auth import get_current_user
from app.routers.users import require_admin_or_auditor_role

def require_write_access(current_user: User = Depends(get_current_user)):
    """Dependency to ensure only admin and asset_manager users can modify assets"""
    if current_user.role not in [UserRole.ADMIN, UserRole.ASSET_MANAGER]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin or Asset Manager role required for this operation"
        )
    return current_user
from app.services.audit_service import log_audit_event
from bson import ObjectId
import uuid

router = APIRouter()

def asset_helper(asset) -> dict:
    """Helper function to convert MongoDB document to dict"""
    if asset:
        asset["_id"] = str(asset["_id"])
        return asset
    return None

@router.post("/", response_model=Asset, status_code=status.HTTP_201_CREATED)
async def create_asset(
    asset: AssetCreate,
    request: Request,
    current_user: User = Depends(require_write_access),
    db = Depends(get_database)
):
    """Create a new asset"""

    # Check permissions
    if current_user.role not in ["admin", "asset_manager"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to create assets"
        )

    # Check if asset_id already exists
    existing_asset = await db.assets.find_one({"asset_id": asset.asset_id})
    if existing_asset:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Asset ID already exists"
        )

    # Check if barcode already exists (if provided)
    if asset.barcode:
        existing_barcode = await db.assets.find_one({"barcode": asset.barcode})
        if existing_barcode:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Barcode already exists"
            )

    # Create asset document
    asset_dict = asset.dict()
    asset_dict.update({
        "_id": ObjectId(),
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
        "created_by": current_user.username,
        "updated_by": current_user.username,
        "photos": [],
        "current_value": asset.purchase_cost,  # Initial value equals purchase cost
        "accumulated_depreciation": 0.0
    })

    # Insert asset
    result = await db.assets.insert_one(asset_dict)

    # Log audit event
    await log_audit_event(
        db, str(current_user.id), current_user.username, AuditAction.CREATE,
        "asset", asset.asset_id,
        {"asset_data": asset.dict()},
        request.client.host if request.client else None
    )

    # Retrieve and return the created asset
    created_asset = await db.assets.find_one({"_id": result.inserted_id})
    return Asset(**asset_helper(created_asset))

@router.get("/")
async def get_assets(
    skip: int = Query(0, ge=0, description="Number of assets to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of assets to return"),
    location: Optional[str] = Query(None, description="Filter by location"),
    custodian: Optional[str] = Query(None, description="Filter by custodian"),
    status: Optional[str] = Query(None, description="Filter by status"),
    depreciation_class: Optional[str] = Query(None, description="Filter by depreciation class"),
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Get list of assets with optional filtering"""
    
    # Build filter query
    filter_query = {}
    if location:
        filter_query["location"] = {"$regex": location, "$options": "i"}
    if custodian:
        filter_query["custodian"] = {"$regex": custodian, "$options": "i"}
    if status:
        filter_query["status"] = status
    if depreciation_class:
        filter_query["depreciation_class"] = depreciation_class
    
    # Get assets
    cursor = db.assets.find(filter_query).skip(skip).limit(limit).sort("created_at", -1)
    assets = await cursor.to_list(length=limit)
    
    # Convert to frontend-compatible format
    frontend_assets = []
    for asset in assets:
        frontend_asset = {
            "_id": str(asset["_id"]),
            "asset_tag": asset.get("asset_id", ""),
            "name": asset.get("description", ""),
            "description": asset.get("description", ""),
            "category": asset.get("category", "Unknown"),
            "location": asset.get("location", ""),
            "department": asset.get("department", ""),
            "assigned_to": asset.get("custodian", ""),
            "purchase_date": asset.get("purchase_date", datetime.utcnow()).isoformat(),
            "purchase_cost": asset.get("purchase_cost", 0),
            "current_value": asset.get("current_value", asset.get("purchase_cost", 0)),
            "depreciation_method": asset.get("depreciation_method", "straight_line"),
            "useful_life_years": asset.get("useful_life_years", 5),
            "salvage_value": asset.get("salvage_value", 0),
            "status": asset.get("status", "active"),
            "condition": asset.get("condition", "good"),
            "warranty_expiry": asset.get("warranty_expiry", datetime.utcnow()).isoformat() if asset.get("warranty_expiry") else None,
            "last_maintenance": asset.get("last_maintenance", datetime.utcnow()).isoformat() if asset.get("last_maintenance") else None,
            "next_maintenance": asset.get("next_maintenance", datetime.utcnow()).isoformat() if asset.get("next_maintenance") else None,
            "barcode": asset.get("barcode", ""),
            "qr_code": asset.get("qr_code", ""),
            "photos": asset.get("photos", []),
            "documents": asset.get("documents", []),
            "created_at": asset.get("created_at", datetime.utcnow()).isoformat(),
            "updated_at": asset.get("updated_at", datetime.utcnow()).isoformat(),
            "created_by": asset.get("created_by", ""),
            "updated_by": asset.get("updated_by", "")
        }
        frontend_assets.append(frontend_asset)

    return frontend_assets

@router.get("/{asset_id}", response_model=Asset)
async def get_asset(
    asset_id: str,
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Get a specific asset by ID"""
    
    asset = await db.assets.find_one({"asset_id": asset_id})
    if not asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )
    
    return Asset(**asset_helper(asset))

@router.put("/{asset_id}", response_model=Asset)
async def update_asset(
    asset_id: str,
    asset_update: AssetUpdate,
    request: Request,
    current_user: User = Depends(require_write_access),
    db = Depends(get_database)
):
    """Update an existing asset"""

    # Check permissions
    if current_user.role not in ["admin", "asset_manager"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to update assets"
        )

    # Check if asset exists
    existing_asset = await db.assets.find_one({"asset_id": asset_id})
    if not existing_asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )

    # Prepare update data
    update_data = {k: v for k, v in asset_update.dict().items() if v is not None}
    if update_data:
        update_data["updated_at"] = datetime.utcnow()
        update_data["updated_by"] = current_user.username

        # Log what changed for audit
        changes = {}
        for key, new_value in update_data.items():
            if key in existing_asset and existing_asset[key] != new_value:
                changes[key] = {
                    "old_value": existing_asset[key],
                    "new_value": new_value
                }

        # Update asset
        await db.assets.update_one(
            {"asset_id": asset_id},
            {"$set": update_data}
        )

        # Log audit event
        await log_audit_event(
            db, str(current_user.id), current_user.username, AuditAction.UPDATE,
            "asset", asset_id,
            {"changes": changes},
            request.client.host if request.client else None
        )

    # Return updated asset
    updated_asset = await db.assets.find_one({"asset_id": asset_id})
    return Asset(**asset_helper(updated_asset))

@router.delete("/{asset_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_asset(
    asset_id: str,
    current_user: User = Depends(require_write_access),
    db = Depends(get_database)
):
    """Delete an asset"""
    
    # Check if asset exists
    existing_asset = await db.assets.find_one({"asset_id": asset_id})
    if not existing_asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )
    
    # Delete asset
    await db.assets.delete_one({"asset_id": asset_id})

@router.get("/barcode/{barcode}", response_model=Asset)
async def get_asset_by_barcode(
    barcode: str,
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Get asset by barcode"""
    
    asset = await db.assets.find_one({"barcode": barcode})
    if not asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset with this barcode not found"
        )
    
    return Asset(**asset_helper(asset))

@router.post("/{asset_id}/photos", status_code=status.HTTP_201_CREATED)
async def upload_asset_photo(
    asset_id: str,
    request: Request,
    current_user: User = Depends(require_write_access),
    db = Depends(get_database),
    file: UploadFile = File(...)
):
    """Upload a photo for an asset"""

    # Check if asset exists
    asset = await db.assets.find_one({"asset_id": asset_id})
    if not asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )

    # Validate file type
    if not file.content_type.startswith("image/"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File must be an image"
        )

    # Create uploads directory if it doesn't exist
    import os
    from app.core.config import settings
    upload_dir = settings.UPLOAD_DIR
    os.makedirs(upload_dir, exist_ok=True)

    # Generate unique filename
    import uuid
    file_extension = file.filename.split(".")[-1] if "." in file.filename else "jpg"
    filename = f"{asset_id}_{uuid.uuid4()}.{file_extension}"
    file_path = os.path.join(upload_dir, filename)

    # Save file
    with open(file_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)

    # Update asset with photo URL
    photo_url = f"/uploads/{filename}"
    await db.assets.update_one(
        {"asset_id": asset_id},
        {
            "$push": {"photos": photo_url},
            "$set": {"updated_at": datetime.utcnow(), "updated_by": current_user.username}
        }
    )

    # Log audit event
    await log_audit_event(
        db, str(current_user.id), current_user.username, AuditAction.PHOTO_UPLOAD,
        "asset", asset_id,
        {"photo_url": photo_url, "filename": file.filename},
        request.client.host if request.client else None
    )

    return {"message": "Photo uploaded successfully", "photo_url": photo_url}

@router.get("/search")
async def search_assets(
    q: str = Query(..., description="Search query"),
    current_user: User = Depends(get_current_user),
    db = Depends(get_database)
):
    """Search assets by description, location, custodian, or asset ID"""

    search_filter = {
        "$or": [
            {"asset_id": {"$regex": q, "$options": "i"}},
            {"description": {"$regex": q, "$options": "i"}},
            {"location": {"$regex": q, "$options": "i"}},
            {"custodian": {"$regex": q, "$options": "i"}},
            {"manufacturer": {"$regex": q, "$options": "i"}},
            {"model": {"$regex": q, "$options": "i"}},
            {"serial_number": {"$regex": q, "$options": "i"}}
        ]
    }

    cursor = db.assets.find(search_filter).limit(50)
    assets = await cursor.to_list(length=50)

    return [Asset(**asset_helper(asset)) for asset in assets]
