from fastapi import APIRouter, Depends, HTTPException
from motor.motor_asyncio import AsyncIOMotorDatabase
from app.core.database import get_database
from app.routers.auth import get_current_user
from app.models.user import User, UserRole
from datetime import datetime, timedelta
from typing import Dict, Any

router = APIRouter()

@router.get("/stats")
async def get_dashboard_stats(
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database)
) -> Dict[str, Any]:
    """Get dashboard statistics with Asset Manager specific metrics"""

    # Get total assets count
    total_assets = await db.assets.count_documents({})

    # Get asset status counts
    active_assets = await db.assets.count_documents({"status": "active"})
    inactive_assets = await db.assets.count_documents({"status": "inactive"})
    disposed_assets = await db.assets.count_documents({"status": "disposed"})
    maintenance_assets = await db.assets.count_documents({"status": {"$in": ["maintenance", "under_maintenance"]}})
    wip_assets = await db.assets.count_documents({"status": "wip"})
    under_verification_assets = await db.assets.count_documents({"status": "under_verification"})
    lost_assets = await db.assets.count_documents({"status": "lost"})

    # Get pending verification count (assets not verified in last 365 days or overdue)
    one_year_ago = datetime.utcnow() - timedelta(days=365)
    pending_verification = await db.assets.count_documents({
        "$or": [
            {"last_verification_date": {"$lt": one_year_ago}},
            {"last_verification_date": {"$exists": False}},
            {"next_verification_due": {"$lt": datetime.utcnow()}}
        ]
    })

    # Get overdue verifications count (Asset Manager specific)
    overdue_verifications = await db.assets.count_documents({
        "next_verification_due": {"$lt": datetime.utcnow()}
    })

    # Get maintenance due count
    maintenance_due = await db.assets.count_documents({
        "next_maintenance": {"$lte": datetime.utcnow()}
    })

    # Calculate total value and current value
    pipeline = [
        {"$group": {
            "_id": None,
            "total_value": {"$sum": "$purchase_cost"},
            "current_value": {"$sum": {"$ifNull": ["$current_value", "$purchase_cost"]}}
        }}
    ]
    value_result = await db.assets.aggregate(pipeline).to_list(1)
    total_value = value_result[0]["total_value"] if value_result else 0
    current_value = value_result[0]["current_value"] if value_result else 0

    # Get assets by category
    category_pipeline = [
        {"$group": {"_id": "$category", "count": {"$sum": 1}}},
        {"$sort": {"count": -1}}
    ]
    category_result = await db.assets.aggregate(category_pipeline).to_list(None)
    assets_by_category = {item["_id"] or "Uncategorized": item["count"] for item in category_result}

    # Get assets by location
    location_pipeline = [
        {"$group": {"_id": "$location", "count": {"$sum": 1}}},
        {"$sort": {"count": -1}}
    ]
    location_result = await db.assets.aggregate(location_pipeline).to_list(None)
    assets_by_location = {item["_id"]: item["count"] for item in location_result}

    # Asset Manager specific metrics
    asset_manager_metrics = {}
    if current_user.role in [UserRole.ADMIN, UserRole.ASSET_MANAGER]:
        # Get verification exceptions
        verification_exceptions = await db.verification_events.count_documents({
            "status": {"$in": ["not_found", "damaged", "exception"]},
            "verification_date": {"$gte": datetime.utcnow() - timedelta(days=30)}
        })

        # Get assets by custodian assignment status
        assets_with_custodians = await db.assets.count_documents({
            "custodians": {"$exists": True, "$ne": []}
        })
        assets_without_custodians = total_assets - assets_with_custodians

        asset_manager_metrics = {
            "wip_assets": wip_assets,
            "overdue_verifications": overdue_verifications,
            "verification_exceptions": verification_exceptions,
            "assets_with_custodians": assets_with_custodians,
            "assets_without_custodians": assets_without_custodians
        }

    return {
        "total_assets": total_assets,
        "active_assets": active_assets,
        "inactive_assets": inactive_assets,
        "disposed_assets": disposed_assets,
        "maintenance_assets": maintenance_assets,
        "wip_assets": wip_assets,
        "under_verification_assets": under_verification_assets,
        "lost_assets": lost_assets,
        "pending_verification": pending_verification,
        "maintenance_due": maintenance_due,
        "total_value": total_value,
        "current_value": current_value,
        "depreciated_value": current_value,
        "assets_by_category": assets_by_category,
        "assets_by_location": assets_by_location,
        **asset_manager_metrics
    }

@router.get("/recent-activities")
async def get_recent_activities(
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get recent audit activities"""
    
    # Get recent audit logs
    cursor = db.audit_logs.find({}).sort("timestamp", -1).limit(10)
    activities = await cursor.to_list(10)
    
    # Convert ObjectId to string
    for activity in activities:
        activity["_id"] = str(activity["_id"])
    
    return activities
