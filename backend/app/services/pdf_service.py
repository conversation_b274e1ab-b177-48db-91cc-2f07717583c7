"""
PDF Generation Service for Asset Registry Reports
"""

import io
from datetime import datetime
from typing import List, Dict, Any
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.pdfgen import canvas
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT


class PDFReportGenerator:
    """Generate PDF reports for various asset registry data"""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self.title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        )
        self.heading_style = ParagraphStyle(
            'CustomHeading',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=12,
            textColor=colors.darkblue
        )
        
    def generate_asset_register_pdf(self, assets: List[Dict], filters: Dict = None) -> bytes:
        """Generate Asset Register PDF report"""
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72,
                              topMargin=72, bottomMargin=18)
        
        # Build story
        story = []
        
        # Title
        title = Paragraph("Asset Register Report", self.title_style)
        story.append(title)
        story.append(Spacer(1, 12))
        
        # Report info
        report_info = [
            ["Report Generated:", datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
            ["Total Assets:", str(len(assets))],
        ]
        
        if filters:
            for key, value in filters.items():
                if value:
                    report_info.append([f"Filter - {key.replace('_', ' ').title()}:", str(value)])
        
        info_table = Table(report_info, colWidths=[2*inch, 3*inch])
        info_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        story.append(info_table)
        story.append(Spacer(1, 20))
        
        # Assets table
        if assets:
            # Table headers
            headers = [
                "Asset ID", "Description", "Location", "Custodian", "Status",
                "Purchase Date", "Purchase Cost", "Current Value", "Category"
            ]
            
            # Table data
            data = [headers]
            for asset in assets:
                row = [
                    asset.get('asset_id', ''),
                    asset.get('description', '')[:30] + ('...' if len(asset.get('description', '')) > 30 else ''),
                    asset.get('location', ''),
                    asset.get('custodian', ''),
                    asset.get('status', ''),
                    asset.get('purchase_date', '')[:10] if asset.get('purchase_date') else '',
                    f"${asset.get('purchase_cost', 0):,.2f}",
                    f"${asset.get('current_value', asset.get('purchase_cost', 0)):,.2f}",
                    asset.get('category', '')
                ]
                data.append(row)
            
            # Create table
            table = Table(data, repeatRows=1)
            table.setStyle(TableStyle([
                # Header styling
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 8),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                
                # Data styling
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 7),
                ('ALIGN', (6, 1), (7, -1), 'RIGHT'),  # Align currency columns right
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                
                # Alternating row colors
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
            ]))
            
            story.append(table)
        else:
            story.append(Paragraph("No assets found matching the specified criteria.", self.styles['Normal']))
        
        # Build PDF
        doc.build(story)
        buffer.seek(0)
        return buffer.getvalue()
    
    def generate_depreciation_summary_pdf(self, summary_data: Dict) -> bytes:
        """Generate Depreciation Summary PDF report"""
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72,
                              topMargin=72, bottomMargin=18)
        
        story = []
        
        # Title
        title = Paragraph("Depreciation Summary Report", self.title_style)
        story.append(title)
        story.append(Spacer(1, 12))
        
        # Report info
        report_info = [
            ["Report Generated:", datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
        ]
        
        info_table = Table(report_info, colWidths=[2*inch, 3*inch])
        info_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        story.append(info_table)
        story.append(Spacer(1, 20))
        
        # Summary by class
        if 'summary_by_class' in summary_data:
            story.append(Paragraph("Depreciation by Asset Class", self.heading_style))
            
            headers = ["Asset Class", "Asset Count", "Total Purchase Cost", "Current Value", "Accumulated Depreciation", "Avg Age (Years)"]
            data = [headers]
            
            for item in summary_data['summary_by_class']:
                row = [
                    item.get('_id', 'Unknown'),
                    str(item.get('asset_count', 0)),
                    f"${item.get('total_purchase_cost', 0):,.2f}",
                    f"${item.get('total_current_value', 0):,.2f}",
                    f"${item.get('total_accumulated_depreciation', 0):,.2f}",
                    f"{item.get('avg_age_years', 0):.1f}"
                ]
                data.append(row)
            
            table = Table(data, repeatRows=1)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 9),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 8),
                ('ALIGN', (2, 1), (-1, -1), 'RIGHT'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
            ]))
            
            story.append(table)
            story.append(Spacer(1, 20))
        
        # Totals summary
        if 'totals' in summary_data:
            totals = summary_data['totals']
            story.append(Paragraph("Overall Summary", self.heading_style))
            
            totals_data = [
                ["Total Assets:", str(totals.get('total_assets', 0))],
                ["Total Purchase Cost:", f"${totals.get('total_purchase_cost', 0):,.2f}"],
                ["Total Current Value:", f"${totals.get('total_current_value', 0):,.2f}"],
                ["Total Depreciation:", f"${totals.get('total_depreciation', 0):,.2f}"],
                ["Overall Depreciation Rate:", f"{totals.get('depreciation_rate', 0):.1f}%"]
            ]
            
            totals_table = Table(totals_data, colWidths=[2.5*inch, 2*inch])
            totals_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('BACKGROUND', (0, 0), (-1, -1), colors.lightgrey),
            ]))
            
            story.append(totals_table)
        
        # Build PDF
        doc.build(story)
        buffer.seek(0)
        return buffer.getvalue()
    
    def generate_verification_results_pdf(self, verification_data: Dict) -> bytes:
        """Generate Verification Results PDF report"""
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72,
                              topMargin=72, bottomMargin=18)
        
        story = []
        
        # Title
        title = Paragraph("Verification Results Report", self.title_style)
        story.append(title)
        story.append(Spacer(1, 12))
        
        # Report period
        period = verification_data.get('period', {})
        start_date = period.get('start_date', '')
        end_date = period.get('end_date', '')

        # Convert datetime objects to strings if needed
        if hasattr(start_date, 'strftime'):
            start_date = start_date.strftime("%Y-%m-%d")
        elif isinstance(start_date, str) and len(start_date) > 10:
            start_date = start_date[:10]

        if hasattr(end_date, 'strftime'):
            end_date = end_date.strftime("%Y-%m-%d")
        elif isinstance(end_date, str) and len(end_date) > 10:
            end_date = end_date[:10]

        report_info = [
            ["Report Generated:", datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
            ["Period From:", start_date],
            ["Period To:", end_date],
        ]
        
        info_table = Table(report_info, colWidths=[2*inch, 3*inch])
        info_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        story.append(info_table)
        story.append(Spacer(1, 20))
        
        # Summary statistics
        summary = verification_data.get('summary', {})
        story.append(Paragraph("Verification Summary", self.heading_style))
        
        summary_data = [
            ["Total Verifications:", str(summary.get('total_verifications', 0))],
            ["Success Rate:", f"{summary.get('success_rate', 0)}%"],
        ]
        
        # Status breakdown
        status_breakdown = summary.get('status_breakdown', {})
        for status, count in status_breakdown.items():
            summary_data.append([f"{status.replace('_', ' ').title()}:", str(count)])
        
        summary_table = Table(summary_data, colWidths=[2.5*inch, 2*inch])
        summary_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (-1, -1), colors.lightgrey),
        ]))
        
        story.append(summary_table)
        story.append(Spacer(1, 20))
        
        # Verifier statistics
        if 'by_verifier' in verification_data:
            story.append(Paragraph("Performance by Verifier", self.heading_style))
            
            headers = ["Verifier", "Total Verifications", "Success Rate"]
            data = [headers]
            
            for verifier in verification_data['by_verifier']:
                total = verifier.get('verifications_count', 0)
                found = verifier.get('found_count', 0)
                success_rate = (found / total * 100) if total > 0 else 0

                row = [
                    verifier.get('_id', 'Unknown'),
                    str(total),
                    f"{success_rate:.1f}%"
                ]
                data.append(row)
            
            verifier_table = Table(data, repeatRows=1)
            verifier_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 9),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 8),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
            ]))
            
            story.append(verifier_table)
        
        # Build PDF
        doc.build(story)
        buffer.seek(0)
        return buffer.getvalue()
    
    def generate_exception_report_pdf(self, exceptions: List[Dict]) -> bytes:
        """Generate Exception Report PDF"""
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72,
                              topMargin=72, bottomMargin=18)
        
        story = []
        
        # Title
        title = Paragraph("Exception Report", self.title_style)
        story.append(title)
        story.append(Spacer(1, 12))
        
        # Report info
        report_info = [
            ["Report Generated:", datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
            ["Total Exceptions:", str(len(exceptions))],
        ]
        
        info_table = Table(report_info, colWidths=[2*inch, 3*inch])
        info_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        story.append(info_table)
        story.append(Spacer(1, 20))
        
        # Exceptions table
        if exceptions:
            headers = ["Asset ID", "Description", "Location", "Exception Type", "Date", "Verifier", "Notes"]
            data = [headers]
            
            for exc in exceptions:
                row = [
                    exc.get('asset_id', ''),
                    exc.get('description', '')[:25] + ('...' if len(exc.get('description', '')) > 25 else ''),
                    exc.get('location', ''),
                    exc.get('status', ''),
                    exc.get('verification_date', '')[:10] if exc.get('verification_date') else '',
                    exc.get('verifier_name', ''),
                    exc.get('notes', '')[:30] + ('...' if len(exc.get('notes', '')) > 30 else '')
                ]
                data.append(row)
            
            table = Table(data, repeatRows=1)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkred),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 8),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 7),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
            ]))
            
            story.append(table)
        else:
            story.append(Paragraph("No exceptions found for the specified period.", self.styles['Normal']))
        
        # Build PDF
        doc.build(story)
        buffer.seek(0)
        return buffer.getvalue()
