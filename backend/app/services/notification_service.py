from typing import List, Dict, Any
from datetime import datetime
from app.core.database import get_database
from app.models.user import User
from app.models.asset import Asset, CustodianAssignment
import logging

logger = logging.getLogger(__name__)

class NotificationService:
    """Service for handling notifications to users"""
    
    @staticmethod
    async def send_custodian_assignment_notification(
        asset: Asset,
        custodian: CustodianAssignment,
        assigned_by_user: User,
        db
    ) -> bool:
        """
        Send notification to a custodian about asset assignment
        
        Args:
            asset: The asset being assigned
            custodian: The custodian assignment details
            assigned_by_user: User who made the assignment
            db: Database connection
            
        Returns:
            bool: True if notification was sent successfully
        """
        try:
            # Create notification record
            notification = {
                "recipient_user_id": custodian.user_id,
                "recipient_email": custodian.email,
                "type": "custodian_assignment",
                "title": f"Asset Assignment: {asset.asset_id}",
                "message": f"You have been assigned as {'primary ' if custodian.is_primary else ''}custodian for asset {asset.asset_id} - {asset.description}",
                "data": {
                    "asset_id": asset.asset_id,
                    "asset_description": asset.description,
                    "asset_location": asset.location,
                    "is_primary": custodian.is_primary,
                    "assigned_by": assigned_by_user.username,
                    "assigned_date": custodian.assigned_date.isoformat()
                },
                "created_at": datetime.utcnow(),
                "read": False,
                "sent": False
            }
            
            # Store notification in database
            await db.notifications.insert_one(notification)
            
            # In a real implementation, you would send email/SMS here
            # For now, we'll just log it
            logger.info(f"Custodian assignment notification sent to {custodian.email} for asset {asset.asset_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send custodian assignment notification: {str(e)}")
            return False
    
    @staticmethod
    async def send_verification_overdue_notification(
        asset: Asset,
        custodians: List[CustodianAssignment],
        db
    ) -> bool:
        """
        Send notification about overdue verification
        
        Args:
            asset: The asset with overdue verification
            custodians: List of custodians to notify
            db: Database connection
            
        Returns:
            bool: True if notifications were sent successfully
        """
        try:
            for custodian in custodians:
                notification = {
                    "recipient_user_id": custodian.user_id,
                    "recipient_email": custodian.email,
                    "type": "verification_overdue",
                    "title": f"Verification Overdue: {asset.asset_id}",
                    "message": f"Asset {asset.asset_id} - {asset.description} has overdue verification. Please verify as soon as possible.",
                    "data": {
                        "asset_id": asset.asset_id,
                        "asset_description": asset.description,
                        "asset_location": asset.location,
                        "last_verification_date": asset.last_verification_date.isoformat() if asset.last_verification_date else None,
                        "next_verification_due": asset.next_verification_due.isoformat() if asset.next_verification_due else None
                    },
                    "created_at": datetime.utcnow(),
                    "read": False,
                    "sent": False,
                    "priority": "high"
                }
                
                await db.notifications.insert_one(notification)
            
            logger.info(f"Verification overdue notifications sent for asset {asset.asset_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send verification overdue notifications: {str(e)}")
            return False
    
    @staticmethod
    async def get_user_notifications(user_id: str, db, unread_only: bool = False) -> List[Dict[str, Any]]:
        """
        Get notifications for a user
        
        Args:
            user_id: User ID to get notifications for
            db: Database connection
            unread_only: Whether to return only unread notifications
            
        Returns:
            List of notifications
        """
        try:
            filter_query = {"recipient_user_id": user_id}
            if unread_only:
                filter_query["read"] = False
            
            cursor = db.notifications.find(filter_query).sort("created_at", -1).limit(50)
            notifications = await cursor.to_list(length=50)
            
            # Convert ObjectId to string
            for notification in notifications:
                notification["id"] = str(notification["_id"])
                del notification["_id"]
            
            return notifications
            
        except Exception as e:
            logger.error(f"Failed to get user notifications: {str(e)}")
            return []
    
    @staticmethod
    async def mark_notification_read(notification_id: str, user_id: str, db) -> bool:
        """
        Mark a notification as read
        
        Args:
            notification_id: Notification ID
            user_id: User ID (for security)
            db: Database connection
            
        Returns:
            bool: True if marked successfully
        """
        try:
            from bson import ObjectId
            
            result = await db.notifications.update_one(
                {
                    "_id": ObjectId(notification_id),
                    "recipient_user_id": user_id
                },
                {
                    "$set": {
                        "read": True,
                        "read_at": datetime.utcnow()
                    }
                }
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Failed to mark notification as read: {str(e)}")
            return False
