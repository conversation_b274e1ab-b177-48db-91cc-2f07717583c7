#!/usr/bin/env python3
"""
Test authentication functionality
"""
import asyncio
import sys
import os
from motor.motor_asyncio import AsyncIOMotorClient
from passlib.context import CryptContext

# Add the app directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings
from app.routers.auth import authenticate_user, get_user_by_username, verify_password

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

async def test_authentication():
    """Test authentication functionality"""
    
    print("Testing authentication...")
    client = AsyncIOMotorClient(settings.MONGODB_URL)
    db = client.asset_registry
    
    try:
        # Test connection
        await client.admin.command('ping')
        print("✓ Connected to MongoDB successfully")
        
        # Test getting admin user
        print("\n1. Testing get_user_by_username...")
        user = await get_user_by_username(db, "admin")
        if user:
            print(f"✓ Found admin user: {user.username}")
            print(f"  Email: {user.email}")
            print(f"  Role: {user.role}")
            print(f"  Status: {user.status}")
        else:
            print("❌ Admin user not found!")
            return
        
        # Test password verification
        print("\n2. Testing password verification...")
        test_passwords = ["AdminPass123!@#", "admin123!@#", "wrongpassword"]
        
        for password in test_passwords:
            is_valid = verify_password(password, user.hashed_password)
            print(f"  Password '{password}': {'✓ Valid' if is_valid else '❌ Invalid'}")
        
        # Test full authentication
        print("\n3. Testing full authentication...")
        auth_result = await authenticate_user(db, "admin", "AdminPass123!@#")
        if auth_result:
            print("✓ Authentication successful!")
            print(f"  User: {auth_result.username}")
            print(f"  Role: {auth_result.role}")
        else:
            print("❌ Authentication failed!")
        
        # Test with wrong password
        auth_result = await authenticate_user(db, "admin", "wrongpassword")
        if not auth_result:
            print("✓ Correctly rejected wrong password")
        else:
            print("❌ Incorrectly accepted wrong password")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
    finally:
        client.close()
        print("\n✓ Database connection closed")

if __name__ == "__main__":
    asyncio.run(test_authentication())
