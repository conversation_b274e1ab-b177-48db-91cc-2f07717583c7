#!/usr/bin/env python3

import asyncio
from datetime import datetime, timedelta
from motor.motor_asyncio import AsyncIOMotorClient
from bson import ObjectId
import random

# Database connection
MONGODB_URL = "mongodb://localhost:27017"
DATABASE_NAME = "asset_registry"

async def populate_sample_data():
    """Populate the database with sample data"""
    
    # Connect to MongoDB
    client = AsyncIOMotorClient(MONGODB_URL)
    db = client[DATABASE_NAME]
    
    print("🚀 Starting to populate sample data...")
    
    # Sample asset data
    categories = ["Laptop", "Desktop", "Monitor", "Printer", "Phone", "Tablet", "Server", "Network Equipment"]
    locations = ["Building A - Floor 1", "Building A - Floor 2", "Building B - Floor 1", "Building B - Floor 2", "Warehouse", "Data Center"]
    departments = ["IT", "Finance", "HR", "Marketing", "Operations", "Sales"]
    statuses = ["active", "maintenance", "disposed"]
    conditions = ["excellent", "good", "fair", "poor"]
    
    # Clear existing assets
    await db.assets.delete_many({})
    print("🗑️  Cleared existing assets")
    
    # Create sample assets
    assets = []
    for i in range(50):
        asset_id = f"AST-{str(i+1).zfill(4)}"
        category = random.choice(categories)
        purchase_date = datetime.utcnow() - timedelta(days=random.randint(30, 1095))
        purchase_cost = random.randint(500, 5000)
        current_value = purchase_cost * random.uniform(0.3, 0.9)  # Depreciated value

        # Some assets have different current locations
        base_location = random.choice(locations)
        current_location = base_location if random.random() > 0.3 else random.choice([
            "Temporary Storage", "Maintenance Room", "User Desk", "Conference Room A",
            "IT Department", "Reception Area", "Training Room"
        ])

        asset = {
            "_id": ObjectId(),
            "asset_id": asset_id,
            "description": f"Sample {category.lower()} for testing - {asset_id}",
            "location": base_location,
            "current_location": current_location,
            "custodian": f"User {random.randint(1, 20)}",
            "barcode": f"BC{str(i+1).zfill(8)}",
            "purchase_date": purchase_date,
            "depreciation_class": "equipment",
            "purchase_cost": purchase_cost,
            "useful_life_years": random.randint(3, 7),
            "salvage_value": purchase_cost * 0.1,
            "current_value": current_value,
            "depreciation_rate": 0.2,
            "status": random.choice(statuses),
            "condition": random.choice(conditions),
            "warranty_expiry": purchase_date + timedelta(days=random.randint(365, 1095)),
            "last_maintenance": datetime.utcnow() - timedelta(days=random.randint(1, 180)),
            "next_maintenance": datetime.utcnow() + timedelta(days=random.randint(30, 365)),
            "photos": [],
            "documents": [],
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "created_by": "admin",
            "updated_by": "admin"
        }
        
        # Add last verification for some assets
        if random.choice([True, False]):
            asset["last_verification"] = datetime.utcnow() - timedelta(days=random.randint(1, 60))
        
        assets.append(asset)
    
    # Insert assets
    result = await db.assets.insert_many(assets)
    print(f"✅ Created {len(result.inserted_ids)} sample assets")
    
    # Create sample verification events
    verification_events = []
    for i in range(20):
        asset = random.choice(assets)
        event = {
            "_id": ObjectId(),
            "asset_id": asset["asset_id"],
            "verifier_id": "admin",
            "verifier_name": "System Administrator",
            "verification_date": datetime.utcnow() - timedelta(days=random.randint(1, 30)),
            "status": random.choice(["found", "missing", "damaged"]),
            "location_found": asset["location"],
            "condition_notes": "Sample verification event",
            "created_at": datetime.utcnow()
        }
        verification_events.append(event)
    
    await db.verification_events.insert_many(verification_events)
    print(f"✅ Created {len(verification_events)} verification events")
    
    # Create sample audit logs
    audit_logs = []
    actions = ["CREATE", "UPDATE", "DELETE", "VERIFY"]
    for i in range(30):
        log = {
            "_id": ObjectId(),
            "action": random.choice(actions),
            "resource_type": "asset",
            "resource_id": random.choice(assets)["asset_id"],
            "user_id": "admin",
            "username": "admin",
            "timestamp": datetime.utcnow() - timedelta(days=random.randint(1, 30)),
            "details": {"sample": "audit log"}
        }
        audit_logs.append(log)
    
    await db.audit_trail.insert_many(audit_logs)
    print(f"✅ Created {len(audit_logs)} audit logs")
    
    # Create sample WIP items
    wip_items = []
    for i in range(10):
        wip = {
            "_id": ObjectId(),
            "name": f"Project {i+1}",
            "description": f"Sample work in progress project {i+1}",
            "category": random.choice(categories),
            "estimated_cost": random.randint(1000, 10000),
            "actual_cost": random.randint(800, 12000),
            "start_date": datetime.utcnow() - timedelta(days=random.randint(30, 180)),
            "expected_completion": datetime.utcnow() + timedelta(days=random.randint(30, 180)),
            "status": random.choice(["planning", "in_progress", "completed"]),
            "progress_percentage": random.randint(10, 100),
            "assigned_to": f"User {random.randint(1, 10)}",
            "department": random.choice(departments),
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        wip_items.append(wip)
    
    await db.wip.insert_many(wip_items)
    print(f"✅ Created {len(wip_items)} WIP items")
    
    print("🎉 Sample data population completed!")
    print("\n📊 Summary:")
    print(f"   • {len(assets)} Assets")
    print(f"   • {len(verification_events)} Verification Events")
    print(f"   • {len(audit_logs)} Audit Logs")
    print(f"   • {len(wip_items)} WIP Items")
    
    # Close connection
    client.close()

if __name__ == "__main__":
    asyncio.run(populate_sample_data())
