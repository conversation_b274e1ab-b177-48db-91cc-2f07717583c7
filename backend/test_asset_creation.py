#!/usr/bin/env python3
"""
Test asset creation functionality
"""

import requests
import json
from datetime import datetime

BASE_URL = "http://localhost:8000"

def test_asset_creation():
    print("🧪 Testing Asset Creation")
    print("=" * 40)
    
    # Login first
    print("1. Logging in...")
    auth_data = {
        "username": "admin",
        "password": "admin123!@#"
    }
    response = requests.post(
        f"{BASE_URL}/api/auth/token",
        data=auth_data,
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
    
    if response.status_code != 200:
        print(f"❌ Login failed: {response.json()}")
        return
    
    token = response.json()["access_token"]
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    print("✅ Login successful")
    
    # Test asset creation
    print("\n2. Creating test asset...")
    test_asset = {
        "asset_id": f"TEST-{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "description": "Test Asset for Frontend",
        "location": "Building A - Floor 1",
        "custodian": "Test User",
        "barcode": f"BC{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "purchase_date": datetime.now().isoformat(),
        "depreciation_class": "equipment",
        "purchase_cost": 1500.0,
        "useful_life_years": 5,
        "status": "active",
        "category": "Desktop",
        "notes": "Created via test script"
    }
    
    print(f"Asset data: {json.dumps(test_asset, indent=2, default=str)}")
    
    response = requests.post(f"{BASE_URL}/api/assets/", headers=headers, json=test_asset)
    print(f"Status: {response.status_code}")
    
    if response.status_code == 201:
        created_asset = response.json()
        print(f"✅ Asset created successfully: {created_asset['asset_id']}")
        return created_asset['asset_id']
    else:
        print(f"❌ Asset creation failed:")
        try:
            error_data = response.json()
            print(json.dumps(error_data, indent=2))
        except:
            print(response.text)
        return None

if __name__ == "__main__":
    test_asset_creation()
