#!/usr/bin/env python3
"""
Script to populate sample assets with proper date fields for testing
"""

import asyncio
import sys
from datetime import datetime, timedelta
import random
from motor.motor_asyncio import AsyncIOMotorClient

# Database settings
MONGODB_URL = "mongodb://localhost:27017"
DATABASE_NAME = "asset_registry"

async def populate_sample_assets():
    """Populate sample assets with proper date fields"""
    
    print("🚀 Populating sample assets with date fields...")
    
    # Connect to MongoDB
    client = AsyncIOMotorClient(MONGODB_URL)
    db = client[DATABASE_NAME]
    
    try:
        # Test connection
        await client.admin.command('ping')
        print("✓ Connected to MongoDB successfully")
        
        # Clear existing assets
        await db.assets.delete_many({})
        print("🗑️  Cleared existing assets")
        
        # Sample data
        categories = ["Laptop", "Desktop", "Monitor", "Printer", "Phone", "Tablet", "Server"]
        locations = ["Building A - Floor 1", "Building A - Floor 2", "Building B - Floor 1", "Building B - Floor 2", "Warehouse", "Data Center"]
        custodians = ["<PERSON>", "<PERSON>", "<PERSON> Johnson", "<PERSON>", "<PERSON>", "<PERSON>"]
        statuses = ["active", "maintenance", "disposed", "under_maintenance"]
        manufacturers = ["Dell", "HP", "Lenovo", "Apple", "Samsung", "Canon", "Epson"]
        
        sample_assets = []
        
        for i in range(1, 21):  # Create 20 sample assets
            asset_id = f"AST-{i:03d}"
            category = random.choice(categories)
            status = random.choice(statuses)
            
            # Generate realistic dates
            purchase_date = datetime.utcnow() - timedelta(days=random.randint(30, 1095))  # 1 month to 3 years ago
            
            # Maintenance date (between purchase and now)
            maintenance_date = purchase_date + timedelta(days=random.randint(90, 730))  # 3 months to 2 years after purchase
            if maintenance_date > datetime.utcnow():
                maintenance_date = datetime.utcnow() - timedelta(days=random.randint(30, 180))
            
            # Next maintenance due (6 months to 1 year after last maintenance)
            next_maintenance_due = maintenance_date + timedelta(days=random.randint(180, 365))
            
            # Disposed date (only for disposed assets)
            disposed_date = None
            if status == "disposed":
                disposed_date = datetime.utcnow() - timedelta(days=random.randint(1, 90))  # Disposed in last 3 months
            
            asset = {
                "asset_id": asset_id,
                "description": f"{random.choice(manufacturers)} {category}",
                "location": random.choice(locations),
                "custodian": random.choice(custodians),
                "barcode": f"BC{1000000 + i}",
                "purchase_date": purchase_date,
                "depreciation_class": "equipment",
                "purchase_cost": round(random.uniform(500, 5000), 2),
                "useful_life_years": random.choice([3, 5, 7]),
                "status": status,
                "serial_number": f"SN{asset_id}{random.randint(1000, 9999)}",
                "manufacturer": random.choice(manufacturers),
                "model": f"Model-{random.randint(100, 999)}",
                "category": category,
                "subcategory": f"{category} Pro",
                "notes": f"Sample {category.lower()} for testing",
                "maintenance_date": maintenance_date,
                "next_maintenance_due": next_maintenance_due,
                "disposed_date": disposed_date,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
                "created_by": "admin",
                "updated_by": "admin",
                "current_value": round(random.uniform(200, 3000), 2),
                "accumulated_depreciation": round(random.uniform(100, 1000), 2),
                "photos": [],
                "custodians": [],
                "last_verification_date": purchase_date + timedelta(days=random.randint(30, 200)),
                "next_verification_due": datetime.utcnow() + timedelta(days=random.randint(30, 365)),
                "verification_frequency_days": 365
            }
            
            sample_assets.append(asset)
        
        # Insert all assets
        result = await db.assets.insert_many(sample_assets)
        print(f"✓ Created {len(result.inserted_ids)} sample assets")
        
        # Show summary by status
        status_counts = {}
        for asset in sample_assets:
            status = asset['status']
            status_counts[status] = status_counts.get(status, 0) + 1
        
        print("\n📊 Asset Summary by Status:")
        for status, count in status_counts.items():
            print(f"   {status}: {count} assets")
        
        print("\n🎉 Sample data population completed successfully!")
        print("\nSample assets include:")
        print("   ✓ Purchase dates (1 month to 3 years ago)")
        print("   ✓ Maintenance dates (realistic intervals)")
        print("   ✓ Next maintenance due dates")
        print("   ✓ Disposed dates (only for disposed assets)")
        print("   ✓ Proper status distribution")
        
    except Exception as e:
        print(f"❌ Error during population: {e}")
        sys.exit(1)
    finally:
        client.close()
        print("🔌 Disconnected from MongoDB")

if __name__ == "__main__":
    asyncio.run(populate_sample_assets())
